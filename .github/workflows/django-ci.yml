name: Django CI

on:
  push:
    branches: [ main, Hotspot_Demo ]
  pull_request:
    branches: [ main, Hotspot_Demo ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
          cache: 'pip'

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libpq-dev python3-dev

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Set environment variables for tests
        run: |
          echo "DJANGO_SECRET_KEY=testsecret" >> $GITHUB_ENV
          echo "DEVELOPMENT=True" >> $GITHUB_ENV
          echo "GANDALF_API_KEY=dummy-key-for-testing" >> $GITHUB_ENV
          echo "GANDALF_API_URL=http://localhost:8000" >> $GITHUB_ENV
          echo "STRIPE_SECRET_KEY_TEST=dummy-stripe-key" >> $GITHUB_ENV
          echo "STRIPE_WEBHOOK_SECRET_TEST=dummy-webhook-secret" >> $GITHUB_ENV
          echo "GOOGLE_API_KEY=dummy-google-key" >> $GITHUB_ENV
          echo "STRIPE_MODE=DEMO" >> $GITHUB_ENV

      - name: Create log directory
        run: |
          mkdir -p logs

      - name: Run Django migrations
        run: |
          python manage.py migrate --settings=djangoProject.settings.ci

      - name: Run Django tests
        run: |
          python manage.py test --settings=djangoProject.settings.ci
