# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Django stuff:
*.log
db.sqlite3
db.sqlite3-journal
hotspotAPI.sqlite

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# VS Code
.vscode/

# PyCharm
.idea/

# Sublime Text
*.sublime-workspace
*.sublime-project

# Static files
/static/
/staticfiles/
/static_root/
/media/

# Backup files
*.bak
*.swp
*~

# Celery
celerybeat-schedule.*
celerybeat.pid

# Documentation
/site
/docs/_build/

# Deployment
deploy.log
/deployment/
/server/
/MyFiles-ignore/
/staticfiles/
/staticfiles/git
__pycache__/
*.pyc

# Git reflog files
et\ --hard\ HEAD@{*}
db.sqlite3
/run_nc811_refresh.py
