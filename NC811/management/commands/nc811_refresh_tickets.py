from django.core.management.base import BaseCommand
from djangoProject.cron import nc811_refresh_tickets


class Command(BaseCommand):
    help = 'Refresh NC811 tickets'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting NC811 ticket refresh...'))
        try:
            nc811_refresh_tickets()
            self.stdout.write(self.style.SUCCESS('NC811 ticket refresh completed successfully!'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error during NC811 ticket refresh: {str(e)}'))
            raise
