from datetime import datetime,timedelta
import pytz
import requests
from django.db import models
from fiberPlan.models import Ticket
import json
from requests.models import PreparedRequest
import os
import logging

class NC811(models.Model):
    # https://docs.djangoproject.com/en/5.0/topics/db/models/
    def add_arguments(self,parser):
        parser.add_argument('session',required=True)

    def __init__(self, mode = 'prod'):
        match mode:
            case 'beta':
                self.NC811_URL = 'https://ncocc-api.newtin.com/api'
            case 'prod':
                self.NC811_URL = 'https://newtina-api.nc811.org/api'
        self.NC811_UID = 'ESC01'
        self.NC811_KEY = '7tT%*SbAsS5Q7='
        self.NC811_TOKEN = 'WoafzTALZuV5LavLZY64wvLdZiu7tbbr'

    def login(self):
        nc811_headers = {"accept": "application/json", "Content-Type": "application/x-www-form-urlencoded"}
        url = f"{self.NC811_URL}/login"
        payload = {'username': self.NC811_UID,'password': self.NC811_KEY}
        s = requests.Session()
        s.post(url, data=payload)
        return s

    def do_request(self, session, request_url='', args=[], method='GET'):
        url = f"{self.NC811_URL}/{request_url}"
        r = session.get(url,params=args)
        return r.json()

    def utc_to_local(self,zulu_dt):
        stamp_utc = datetime.fromisoformat(zulu_dt[:-1])
        local_tz = pytz.timezone('America/New_York')
        return stamp_utc.astimezone(local_tz).strftime('%a %b %d %Y %I:%M:%S%p')

    # Database Calls
    def get_tickets_updated_at(self):
        """
        Parse cron.log to find the last time NC811 ticket refresh ran
        Returns formatted date/time string of last run
        """
        try:
            # Assuming cron.log is in a standard location
            log_path = "/var/log/hotspot/cron.log"
            if not os.path.exists(log_path):
                # Fallback to a relative path if needed
                log_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "logs/cron.log")
            
            last_start_time = None
            
            with open(log_path, 'r') as log_file:
                for line in reversed(log_file.readlines()):
                    if "NC811: Ticket Refresh started" in line:
                        # Extract the timestamp from the log line
                        # Format: "INFO NC811: Ticket Refresh started 10:00AM on May 27, 2025"
                        time_str = line.split("NC811: Ticket Refresh started ")[1].strip()
                        last_start_time = time_str
                        break
            
            if last_start_time:
                return last_start_time
            else:
                # Fallback to the original implementation if log parsing fails
                myticket = Ticket.objects.all()[:1]
                return myticket[0].updated_at
            
        except Exception as e:
            # Log the error but don't crash
            logging.error(f"Error parsing cron log: {str(e)}")
            # Fallback to original implementation
            myticket = Ticket.objects.all()[:1]
            return myticket[0].updated_at

    def get_ticket(self, ticket_id):
        session = self.login()
        ticket = self.do_request(session, 'tickets/' + ticket_id )
        ticket['original_date'] = self.utc_to_local(ticket['original_date'])
        ticket['replace_by_date'] = self.utc_to_local(ticket['replace_by_date'])
        ticket['started'] = self.utc_to_local(ticket['started'])
        ticket['completed'] = self.utc_to_local(ticket['completed'])
        ticket['work_date'] = self.utc_to_local(ticket['work_date'])
        ticket['expires'] = self.utc_to_local(ticket['expires'])
        ticket['update_by'] = self.utc_to_local(ticket['update_by'])
        ticket['response_due'] = self.utc_to_local(ticket['response_due'])

        return ticket




    def get_ticket_responses(self,ticket_id):
        session = self.login()
        responses = self.do_request(session, 'tickets/' + ticket_id + '/responses')
        return responses['responses']

    def refresh_tickets(self):
        session = self.login()
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        end_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        now = datetime.now().strftime('%m-%d-%Y')
        tickets = self.do_request(session, 'tickets/responses', {'start': start_date, 'end': end_date})
        result = []
        count = 0
        for ticket in tickets['data']:
            count += 1
            work_date = (datetime.strptime(ticket['work_date'][:19], '%Y-%m-%dT%H:%M:%S') - timedelta(hours=4)).strftime('%m-%d-%Y %H:%M:%S')
            expires = (datetime.strptime(ticket['expires'][:19], '%Y-%m-%dT%H:%M:%S') - timedelta(hours=4)).strftime('%m-%d-%Y %H:%M:%S')
            update_by = (datetime.strptime(ticket['update_by'][:19], '%Y-%m-%dT%H:%M:%S') - timedelta(hours=4)).strftime('%m-%d-%Y %H:%M:%S')
            response_due = (datetime.strptime(ticket['response_due'][:19], '%Y-%m-%dT%H:%M:%S') - timedelta(hours=4)).strftime('%m-%d-%Y %H:%M:%S')
            result.append({
                'ticket': ticket['ticket'],
                'priority': ticket['priority'],
                'name': ticket['name'],
                'street': ticket['street'],
                'cross': ticket['cross1'],
                'place': ticket['place'],
                'done_for': ticket['done_for'],
                'work_type': ticket['work_type'],
                'work_date': work_date,
                'expires': expires,
                'update_by': update_by,
                'response_due': response_due,
            })
        # save tickets
        self.save_tickets(result)
        return count

    def get_tickets(self):
        result = Ticket.objects.all() \
            .values('ticket', 'priority', 'name', 'street', 'cross', 'place', 'done_for', 'work_type', 'expires', 'update_by', 'work_date', 'response_due','updated_at')
        return result
    # dump ticket table and store new values
    def save_tickets(self, tickets):
        #Ticket.objects.all().delete()
        for ticket in tickets:
            if not Ticket.objects.filter(ticket=ticket['ticket']).exists():
                v = Ticket(
                    ticket=ticket['ticket'],
                    priority=ticket['priority'],
                    name=ticket['name'],
                    street=ticket['street'],
                    cross=ticket['cross'],
                    place=ticket['place'],
                    done_for=ticket['done_for'],
                    work_type=ticket['work_type'],
                    work_date=datetime.strptime(ticket['work_date'],'%m-%d-%Y %H:%M:%S'),
                    expires=datetime.strptime(ticket['expires'],'%m-%d-%Y %H:%M:%S'),
                    update_by=datetime.strptime(ticket['update_by'],'%m-%d-%Y %H:%M:%S'),
                    response_due=datetime.strptime(ticket['response_due'],'%m-%d-%Y %H:%M:%S'),
                )
                v.save()


    def update_response(self, ticket_id, response_id, status):

        if ticket_id is None or status is None: raise ValueError("Missing ticket or status information.")

        session = self.login()

        update_endpoint = f"tickets/{ticket_id}/responses"

        payload = {"memberCode": self.NC811_UID, "memberResponseCode": status, "comments": "Update"}

        nc811_headers = {"accept": "application/json", "Content-Type": "application/json"}

        url = f"{self.NC811_URL}/{update_endpoint}" # Full API endpoint

        print(f"payload = {payload}")
        response = session.post(url, json=payload, headers=nc811_headers)
        #print(f"response = {(response}")
        response_content = response.content
        content_str = response_content.decode('utf-8')
        content_dict = json.loads(content_str)
        apiMessage = None

        response_message = content_dict.get("message")

        if response_message is None:
            print(f"Error: No message in response.")


        apiMessage = response_message

        print (f"apiMessage = {apiMessage}")
        if response.status_code == 200:
            return apiMessage
        else:
            print(f" Error response: {response}")
            response.raise_for_status()


def debugprint(response):
    """
    Prints detailed debugging information about a response object to the console.
    Compatible with Django HttpResponse and other response types.

    Args:
        response: A response object (e.g., HttpResponse, JsonResponse, or DRF Response).
    """
    print("\n=== Debug Print: Response Object ===")

    # Status Code
    print(f"Status Code: {getattr(response, 'status_code', 'N/A')}")

    # Reason Phrase (if available)
    print(f"Reason Phrase: {getattr(response, 'reason_phrase', 'N/A')}")

    # Content (decoded if possible)
    try:
        content = response.content.decode('utf-8') if hasattr(response,
                                                              'content') and response.content else "No content"
        print(f"Content: {content}")
    except UnicodeDecodeError:
        print(
            f"Content: [Binary or non-UTF-8 data, length={len(response.content) if hasattr(response, 'content') else 0} bytes]")
    except AttributeError:
        print("Content: [Not accessible or unsupported type]")

    # Headers (with fallback if items() or get() aren't available)
    print("Headers:")
    if hasattr(response, 'items') and callable(response.items):
        for key, value in response.items():
            print(f"  {key}: {value}")
    elif hasattr(response, 'headers') and isinstance(response.headers, dict):
        # Fallback for DRF Response or similar objects
        for key, value in response.headers.items():
            print(f"  {key}: {value}")
    else:
        print("  [Headers not accessible or unsupported type]")

    # Content-Type (with fallback)
    content_type = "Not specified"
    if hasattr(response, 'get') and callable(response.get):
        content_type = response.get('Content-Type', 'Not specified')
    elif hasattr(response, 'headers') and 'Content-Type' in response.headers:
        content_type = response.headers['Content-Type']
    elif hasattr(response, 'content_type'):
        content_type = response.content_type
    print(f"Content-Type: {content_type}")

    # Cookies (if any)
    if hasattr(response, 'cookies') and response.cookies:
        print("Cookies:")
        for name, cookie in response.cookies.items():
            print(f"  {name}: {cookie.value} (Attrs: {cookie.items()})")
    else:
        print("Cookies: None set")

    # Response Class/Type
    print(f"Response Type: {response.__class__.__name__}")

    # Streaming Response Check
    is_streaming = getattr(response, 'streaming', False)
    print(f"Is Streaming: {is_streaming}")

    # Length of content (if not streaming and content exists)
    if not is_streaming and hasattr(response, 'content') and response.content:
        print(f"Content Length: {len(response.content)} bytes")

    print("===================================\n")