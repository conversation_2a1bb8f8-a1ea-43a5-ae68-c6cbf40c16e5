import sys
from auditlog.signals import audit_log_signal
from django.http import JsonResponse
from django.shortcuts import render, redirect
from django.views.decorators.csrf import csrf_protect
from django.views.generic import ListView
from django.conf import settings
from django.http import JsonResponse
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User, Permission
from django.contrib.contenttypes.models import ContentType
from fiberPlan.models import Vault
import os
from dotenv import load_dotenv, dotenv_values
import googlemaps
import json
import csv
from fiberPlan.myCostQuest import myCostQuest
from datetime import datetime
from NC811.nc811Class import NC811
import logging
from django.shortcuts import render
from djangoProject.cron import nc811_refresh_tickets

# Create your views here.

import requests  # Import the requests library
from django.http import JsonResponse
status_options = [
    {"10": "No conflict, utility is outside of stated work area"},
    {"20": "Marked"},
    {"30": "Not complete"},
    {"32": "Locate not complete, additional communication required, unable to contact excavator"},
    {"40": "Could not gain access"},
    {"45": "Railroad facility not marked"},
    {"50": "Critical facility not marked"},
    {"55": "Critical facility marked"},
    {"60": "Marking schedule agreed"},
    {"70": "Excavation completed early"},
    {"80": "Master contractor responsible"},
    {"90": "Survey request, facilities marked"},
    {"92": "Survey request, no facilities"},
    {"94": "Survey request, records provided"},
    {"98": "Survey request, access to records"},
    {"100": "Request denied, Homeland Security"},
    {"110": "Sub-aqueous facilities present"},
    {"888": "Extraordinary circumstances"},
    {"999": "No response by required time"}
]

def flatten_json(data):
    flat_data = {}
    for key, value in data.items():
        # Check if the value is a dictionary or a list (i.e., nested JSON)
        if not isinstance(value, (dict, list)):
            flat_data[key] = value
    return flat_data

@login_required(login_url='fiberPlan:login')
def get_printable_ticket(request, id):
    load_dotenv()
    num_visits = request.session.get('num_visits', 0)
    request.session['num_visits'] = num_visits + 1
    nc811_api = NC811()
    session = nc811_api.login()
    ticket = nc811_api.get_ticket(str(id))
    # Correct the date format for parsing the 'ticket_response_due' string
    date_format = "%a %b %d %Y %I:%M:%S%p"  # Matches 'Wed Feb 12 2025 04:59:00AM'
    ticket_response_due = ticket['response_due']

    # Wrap conversion in a try-except block to handle potential parsing errors
    try:
        response_due_date = datetime.strptime(ticket_response_due, date_format)
    except ValueError as e:
        # Log error and set a fallback or default value
        print(f"Error parsing date: {e}")
        response_due_date = None

    # Determine if the ticket is past due
    isPastDue = "true" if response_due_date and response_due_date < datetime.now() else "false"
    ticket['isPastDue'] = isPastDue
    flattened_ticket = {k: v for k, v in ticket.items() if isinstance(v, (str, int, float, bool))}
    printable_obj = {key: flattened_ticket[key] for key in ["ticket", "priority", "caller", "location"]}
    print_ticket_json = printable_obj

    print(f"flattened ticket: {print_ticket_json}")
    key = os.getenv('GOOGLE_API_KEY')
    coordinates = ticket['work_area']['geometry']['coordinates'][0]
    polygon = []
    for coordinate in coordinates:
        polygon.append({'lng': coordinate[0], 'lat': coordinate[1]})
    context = {'id': id, 'ticket': ticket, 'key': key, 'polygon': polygon, 'num_visits': num_visits,
               'print_ticket_json': print_ticket_json}
    return render(request, 'nc811/ticket_popup.html', context)


@login_required(login_url='fiberPlan:login')
def ticket(request, id):
    load_dotenv()
    num_visits = request.session.get('num_visits', 0)
    request.session['num_visits'] = num_visits + 1
    nc811_api = NC811()
    session = nc811_api.login()
    ticket = nc811_api.get_ticket(str(id))
    date_format = "%a %b %d %Y %I:%M:%S%p"  # Matches 'Wed Feb 12 2025 04:59:00AM'
    ticket_response_due = ticket['response_due']

    # Wrap conversion in a try-except block to handle potential parsing errors
    try:
        response_due_date = datetime.strptime(ticket_response_due, date_format)
    except ValueError as e:
        # Log error and set a fallback or default value
        print(f"Error parsing date: {e}")
        response_due_date = None

    # Determine if the ticket is past due
    isPastDue = "true" if response_due_date and response_due_date < datetime.now() else "false"
    ticket['isPastDue'] = isPastDue
    print(f"flattened json=: {flatten_json(ticket)}")
    key = os.getenv('GOOGLE_API_KEY')
    coordinates = ticket['work_area']['geometry']['coordinates'][0]
    polygon = []
    for coordinate in coordinates:
        polygon.append({'lng': coordinate[0], 'lat': coordinate[1]})
    context = {'id': id, 'ticket': ticket, 'key': key, 'polygon': polygon, 'num_visits': num_visits}
    return render(request, 'nc811/ticket.html', context)


@login_required(login_url='fiberPlan:login')
def refresh_tickets(request):
    nc811_api = NC811()
    nc811_api.refresh_tickets()
    ticket_updated = nc811_api.get_tickets_updated_at()
    context = {'ticket_updated': ticket_updated}
    return render(request, 'nc811/tickets-summary.html', context)


@login_required(login_url='fiberPlan:login')
def tickets_summary(request):
    num_visits = request.session.get('num_visits', 0)
    request.session['num_visits'] = num_visits + 1
    nc811_api = NC811()
    ticket_updated = nc811_api.get_tickets_updated_at()
    context = {'ticket_updated': ticket_updated, 'num_visits': num_visits}
    return render(request, 'nc811/tickets-summary.html', context)


def get_status_value(key):
    for status_dict in status_options:
        if key in status_dict:
            print(f"key: {key} value: {status_dict[key]}")
            return status_dict[key]
    return None  # Return None if not found

@login_required(login_url='fiberPlan:login')
# NC811 function which changes the ticket status in the newtin NC811 system.
def update_response_status(request, ticket_id, response_id, status):
    try:
        # Log input parameters for debugging
        print("Ticket ID:", ticket_id)
        print("Response ID:", response_id)
        print("Status:", status)

        # Initialize API client
        nc811_api = NC811()

        # Make API call
        result = nc811_api.update_response(ticket_id=ticket_id, response_id=response_id, status=status)
        print("status check:")
        change_to = get_status_value(status)
        print("response_id check:")
        change_from = "Pending" if get_status_value(response_id) in [None, "None"] else get_status_value(response_id)

        # Compare and log changes
        changes = {
            change_from: change_from,
            change_to: change_to
        }

        audit_log_signal.send(
            sender=None,
            request=request,
            model_name='Ticket',
            object_id=ticket_id,
            action="TICKET_STATUS_UPDATE",
            extra_data=changes
        )

        # Return success message
        return JsonResponse({
            "message": result if result else f"Status successfully changed from {change_from} to {change_to}"
        }, status=200)

    except requests.exceptions.HTTPError as e:
        # Handle HTTP-specific errors
        error_message = f"API request failed: {e.response.text}"
        return JsonResponse({
            "message": error_message
        }, status=e.response.status_code)

    except requests.exceptions.ConnectionError as e:
        # Handle connection errors
        return JsonResponse({
            "message": "Failed to connect to the API service"
        }, status=503)

    except requests.exceptions.Timeout as e:
        # Handle timeout errors
        return JsonResponse({
            "message": "API request timed out"
        }, status=504)

    except ValueError as e:
        # Handle invalid data/format errors
        return JsonResponse({
            "message": f"Invalid data provided: {str(e)}"
        }, status=400)

    except Exception as e:
        # Catch-all for unexpected errors
        return JsonResponse({
            "message": f"An unexpected error occurred: {str(e)}"
        }, status=500)

@login_required(login_url='fiberPlan:login')
def json_response(request):
    param = request.GET.get('param', 'empty')
    result = []
    match param:
        case 'get_all_vaults':
            result = list(Vault.objects.all() \
                          .values('id', 'name', 'lat', 'lng', 'address', 'is_mini', 'is_enc', 'notes'))
            for vault in result:
                vault['name'] = '<a href="vault/' + str(vault['id']) + '" target="_self">' + str(vault['name']) + '</a>'
        case 'get_map_data':
            result = list(Vault.objects \
                          .values('id', 'name', 'lat', 'lng', 'address', 'is_mini', 'is_enc', 'notes'))
        case 'get_address_lookup':
            nc811_api = NC811()
            session = nc811_api.login()
            payload = {
                'state': 'NC',
                'county': 'Camden',
                'place': 'Shiloh',
                'street': '101-199 Beech Neck Rd'
            }
            result = nc811_api.do_request(session, 'map-features/address-lookup', payload)
        case 'get_updated_at':
            nc811_api = NC811()
            result = nc811_api.get_tickets_updated_at()
        case 'nightly_ticket_refresh':
            nc811_api = NC811()
            result = nc811_api.refresh_tickets()
        case 'get_ticket':
            ticket = request.GET.get('ticket')
            nc811_api = NC811()
            result = nc811_api.get_ticket(ticket)
        case 'get_ticket_responses':
            ticket = request.GET.get('ticket')
            nc811_api = NC811()
            responses = nc811_api.get_ticket_responses(ticket)
            for response in responses:
                if ('responded' in response):
                    response['responded'] = nc811_api.utc_to_local(response['responded'])
                else:
                    response['responded'] = "Pending"

            # print(f"responses: {responses}")
            result = ensure_description(responses)
        case 'get_tickets':
            nc811_api = NC811()
            result = list(nc811_api.get_tickets())
        case _:
            result = {'result': False}
    return JsonResponse(result, safe=False)

def ensure_description(json_data):
    # Load JSON if it's a string
    if isinstance(json_data, str):
        data = json.loads(json_data)
    else:
        data = json_data

    # Iterate through each record and ensure "description" exists
    for record in data:
        if "description" not in record:
            record["description"] = "No description available"

    return data

@login_required(login_url='fiberPlan:login')
def update_tickets_now(request):
    """
    AJAX endpoint to manually trigger ticket refresh using the cron function
    """
    try:
        # Run the cron function to refresh tickets
        nc811_refresh_tickets()

        # Return success response
        return JsonResponse({
            "success": True,
            "message": "Tickets Updated!"
        }, status=200)

    except Exception as e:
        # Return error response
        return JsonResponse({
            "success": False,
            "message": f"Error updating tickets: {str(e)}"
        }, status=500)
