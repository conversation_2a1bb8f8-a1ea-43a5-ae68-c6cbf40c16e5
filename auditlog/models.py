from django.db import models
from django.contrib.auth.models import User

class AuditLog(models.Model):
    ACTION_CHOICES = [
        ('CREATE', 'Create'),
        ('UPDATE', 'Update'),
        ('DELETE', 'Delete')
    ]

    model_name = models.Char<PERSON>ield(max_length=100)  # The name of the model (e.g., 'User', 'Product')
    object_id = models.IntegerField(null=True, blank=True)
    action = models.CharField(max_length=10, choices=ACTION_CHOICES)  # Create, Update, or Delete
    timestamp = models.DateTimeField(auto_now_add=True)  # Automatically set to current time
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    extra_data = models.JSONField(null=True, blank=True)

    # User who performed the action

    def __str__(self):
        return f'{self.action} on {self.model_name} {self.object_id} by {self.user}'

