
from django.db.models.signals import post_save
from django.contrib.auth.models import User
from django.contrib.auth.models import AnonymousUser
from django.dispatch import receiver, Signal
from .models import AuditLog

audit_log_signal = Signal()


# Signal handler for password change
@receiver(post_save, sender=User)
def log_password_change(sender, instance, **kwargs):
    if 'password' in instance.get_deferred_fields():  # Detect password changes
        audit_log_signal.send(
            sender=User,
            request=None,  # No request object available in post_save signal
            model_name='User',
            object_id=instance.id,
            action='PASSWORD_CHANGE',
            extra_data={'details': 'User password changed.'}
        )


# Signal handler for logging audit entries
@receiver(audit_log_signal)
def log_audit_entry(sender, **kwargs):
    request = kwargs.get('request')
    model_name = kwargs.get('model_name', '')
    object_id = kwargs.get('object_id', None)
    action = kwargs.get('action', 'UNKNOWN')
    extra_data = kwargs.get('extra_data', {})

    # Correctly format the changes
    if isinstance(extra_data, dict):
        changes_str = ', '.join(
            [
                f"{field}: {data['old']} → {data['new']}" if isinstance(data, dict) and 'old' in data and 'new' in data
                else f"{field}: {data}"
                for field, data in extra_data.items()
            ]
        )
    else:
        changes_str = str(extra_data)

    # Ensure object_id is a valid integer
    if object_id and isinstance(object_id, int):
        AuditLog.objects.create(
            model_name=model_name,
            object_id=object_id,
            action=action,
            user=request.user if not isinstance(request.user, AnonymousUser) else None,
            extra_data={'details': changes_str} if changes_str else {}
        )
    else:
        print(f"Warning: Missing or invalid object_id for {request.path}. Audit log entry skipped.")
