#!/bin/bash

set -euo pipefail
cd /srv/www/django.esvc.us/hotspot

# Ensure the directory is marked safe for git
/usr/bin/git config --global --add safe.directory /srv/www/django.esvc.us/hotspot

echo "[$(date)] Starting auto-deploy..." >> deploy.log

# Activate virtualenv
source venv/bin/activate

# Discard any local changes to match GitHub repo
/usr/bin/git reset --hard HEAD >> deploy.log 2>&1
/usr/bin/git clean -fd >> deploy.log 2>&1

# Pull latest code
/usr/bin/git pull origin main >> deploy.log 2>&1

# Run Django maintenance commands
python manage.py migrate --noinput >> deploy.log 2>&1
python manage.py collectstatic --noinput >> deploy.log 2>&1

# Restart Gunicorn
sudo systemctl restart gunicorn >> deploy.log 2>&1

echo "[$(date)] Auto-deploy finished." >> deploy.log
