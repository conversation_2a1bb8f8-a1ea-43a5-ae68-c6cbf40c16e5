3749784 - Add settings.py
c32c1ea - push/commit
2a99ed6 - Final changes put in for compact ticket data table, 2 columns for key/value pairs, message returned to client upon updating status code from_code to to_code, revert auto status column change when selecting from drop down on client.
a32f13e - API/UI Update to layout, table size, map size, also handling API calls and messaging. Properly enabling/disabling "update" button for response status.
56d5e3b - API/UI updates
000f7ff - API/UI CHANGES
dfa144a - API changes WIP. Returns error info, checks past due for options on changing status
a788a8b - ticket template showing description/status as drop down.