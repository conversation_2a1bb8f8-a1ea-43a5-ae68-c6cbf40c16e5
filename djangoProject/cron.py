import logging
import datetime
import time
import os
import sys
from NC811.nc811Class import NC811
from englishTowers.django_gandalf_client import GandalfAPIClient
import nmap

# Use the correct logger name to match settings.py
logger = logging.getLogger('crontab')

def nc811_refresh_tickets():
    logger.info('NC811: Ticket Refresh started ' + datetime.datetime.now().strftime("%I:%M%p on %B %d, %Y"))
    nc811_api = NC811()
    tickets = nc811_api.refresh_tickets()
    logger.info('NC811: Ticket Refresh Ended. ' + str(tickets) + ' tickets found')

def et_update_routers():
    logger.info('ET Routers: Maintenance started ' + datetime.datetime.now().strftime("%I:%M%p on %B %d, %Y"))
    gandalf_client = GandalfAPIClient()
    nm = nmap.PortScanner()
    nm.scan(hosts='*********/23', arguments='-sn')
    hosts_list = [(x, nm[x]['status']['state']) for x in nm.all_hosts()]
    count = 0
    for host, status in hosts_list:
        hostname = gandalf_client.get_router_hostname(host)
        if hostname != 'Connection Error':
            count += 1
            gandalf_client.update_nas('et' + hostname, host)
    logger.info('ET Routers: ' + str(count) + ' Routers have been found and updated')

def et_rental_networks():
    lockfile = "/tmp/et_rental_networks.lock"
    if os.path.exists(lockfile):
        logger.warning("ET Rental: Previous job still running, exiting early to prevent overlap.")
        return
    open(lockfile, 'w').close()
    try:
        logger.info('ET Rental: Maintenance started ...')
        start = time.time()
        gandalf_client = GandalfAPIClient()
        results = gandalf_client.get_active_networks()
        logger.info(f"Found {len(results)} active networks")

        for idx, result in enumerate(results):
            t0 = time.time()
            logger.info(f"Processing unit {result['unit']}")
            # --- Begin original logic here ---
            # If daysLeft == 0, perform clean-up/reset
            if result.get('daysLeft', None) == 0:
                unit = str(result['unit'])   # e.g., et708
                ipaddress = gandalf_client.get_unit_router_ip(unit[2:])
                gandalf_client.reset_access_point(ipaddress)
                gandalf_client.router_turn_rental_off(ipaddress)
                logger.info('ET Rental. Unit ' + unit[2:] + ' has been cleaned')
            # --- End original logic ---
            logger.info(f"Done with {result['unit']} in {time.time() - t0:.2f}s")

            # Optional: throttle API requests to reduce load on Gandalf API
            time.sleep(0.25)  # 250ms per unit, adjust/remove as needed

            # [Optional: For diagnostics/testing, limit the number of units per run]
            # if idx >= 10:
            #     logger.warning("ET Rental: Processed 10 units, exiting early for test safety.")
            #     break

        duration = time.time() - start
        logger.info(f"ET Rental: Maintenance finished in {duration:.2f}s")
        if duration > 300:  # 5 minutes
            logger.warning(f"ET Rental: WARNING – Maintenance took over 5 minutes!")

    finally:
        if os.path.exists(lockfile):
            os.remove(lockfile)
