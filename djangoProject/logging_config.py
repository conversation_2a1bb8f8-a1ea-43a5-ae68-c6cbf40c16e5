import os
import sys
import stat

def get_log_dir():
    IS_DEVELOPMENT = sys.platform.startswith('win') or 'DEVELOPMENT' in os.environ
    if IS_DEVELOPMENT:
        LOG_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
    else:
        LOG_DIR = '/var/log/hotspot'
    
    # Create the directory if it doesn't exist
    if not os.path.exists(LOG_DIR):
        try:
            os.makedirs(LOG_DIR, exist_ok=True)
            # Set permissions to 755 (rwxr-xr-x)
            os.chmod(LOG_DIR, stat.S_IRWXU | stat.S_IRGRP | stat.S_IXGRP | stat.S_IROTH | stat.S_IXOTH)
        except Exception as e:
            print(f"Warning: Could not create or set permissions on log directory: {e}")
            # Fallback to a directory we know we can write to
            LOG_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
            os.makedirs(LOG_DIR, exist_ok=True)
    
    return LOG_DIR

def get_log_path(filename):
    log_dir = get_log_dir()
    log_path = os.path.join(log_dir, filename)
    
    # Try to create an empty file if it doesn't exist
    if not os.path.exists(log_path):
        try:
            with open(log_path, 'a'):
                pass
            # Set permissions to 644 (rw-r--r--)
            os.chmod(log_path, stat.S_IRUSR | stat.S_IWUSR | stat.S_IRGRP | stat.S_IROTH)
        except Exception as e:
            print(f"Warning: Could not create or set permissions on log file {filename}: {e}")
    
    return log_path
