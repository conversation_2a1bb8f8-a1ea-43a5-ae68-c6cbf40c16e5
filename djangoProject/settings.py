import os
import sys
import socket
from pathlib import Path
from django.contrib import messages
from logging.handlers import RotatingFileHandler
from dotenv import load_dotenv

# Load environment variables from .env file ...
load_dotenv()

# Determine if we're running in development (Windows or explicit DEVELOPMENT flag)
IS_DEVELOPMENT = sys.platform.startswith('win') or 'DEVELOPMENT' in os.environ

# Get the current hostname for server-specific settings
THIS_HOST = socket.getfqdn()

# Base directory of the project
BASE_DIR = Path(__file__).resolve().parent.parent

# Base log directory
if IS_DEVELOPMENT:
    LOG_DIR = os.path.join(BASE_DIR, 'logs')
    os.makedirs(LOG_DIR, exist_ok=True)
else:
    LOG_DIR = '/var/log/hotspot'

# Secret key and debug mode
SECRET_KEY = os.getenv('DJANGO_SECRET_KEY', os.environ.get('SECRET_KEY', 'fallback-insecure-key-for-dev'))
DEBUG = os.getenv('DJANGO_DEBUG', 'False') == 'True'

# Allowed hosts (comma-separated in env var)
ALLOWED_HOSTS = os.getenv('ALLOWED_HOSTS', 'legolas.esvc.us').split(',')

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'fiberPlan.apps.FiberplanConfig',
    'englishTowers.apps.EnglishTowersConfig',
    'django_bootstrap5',
    'auditlog',
    'profiles.apps.ProfilesConfig',
    'django_crontab',
    'graphos',
    'NC811.apps.Nc811Config',
    'samUisp.apps.SamuispConfig',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.cache.UpdateCacheMiddleware',
    'django.middleware.common.CommonMiddleware',
    # 'django.middleware.cache.FetchFromCacheMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'djangoProject.urls'

# Configure CRONJOBS based on environment variables
CRONJOBS = []

if os.getenv('CRONJOBS_NC811_REFRESH'):
    CRONJOBS.append((os.getenv('CRONJOBS_NC811_REFRESH'), 'djangoProject.cron.nc811_refresh_tickets'))

# Conditionally add other cronjobs if they're enabled in the environment
if os.getenv('CRONJOBS_ET_UPDATE_ROUTERS'):
    CRONJOBS.append((os.getenv('CRONJOBS_ET_UPDATE_ROUTERS'), 'djangoProject.cron.et_update_routers'))

if os.getenv('CRONJOBS_ET_RENTAL_NETWORKS'):
    CRONJOBS.append((os.getenv('CRONJOBS_ET_RENTAL_NETWORKS'), 'djangoProject.cron.et_rental_networks'))

if os.getenv('CRONJOBS_FB_UPDATE_CUSTOMERS'):
    CRONJOBS.append((os.getenv('CRONJOBS_FB_UPDATE_CUSTOMERS'), 'djangoProject.cron.fb_update_customers'))

CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.filebased.FileBasedCache',
        'LOCATION': os.path.join(BASE_DIR, 'django_cache'),
    }
}

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

TEMPLATE_CONTEXT_PROCESSORS = [
    'django.contrib.auth.context_processors.auth',
]

WSGI_APPLICATION = 'djangoProject.wsgi.application'

# Logging configuration
# Logging configuration (safe for CI and dev environments)
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'ERROR',
            'propagate': True,
        },
        'stripe': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'crontab': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# Only add file handlers if LOG_DIR exists (i.e., in production or local dev)
if os.path.exists(LOG_DIR):
    LOGGING['handlers']['error_file'] = {
        'level': 'ERROR',
        'class': 'logging.handlers.RotatingFileHandler',
        'filename': os.path.join(LOG_DIR, 'errors.log'),
        'maxBytes': 5 * 1024 * 1024,
        'backupCount': 3,
        'formatter': 'verbose',
    }
    LOGGING['handlers']['info_file'] = {
        'level': 'INFO',
        'class': 'logging.handlers.RotatingFileHandler',
        'filename': os.path.join(LOG_DIR, 'info.log'),
        'maxBytes': 5 * 1024 * 1024,
        'backupCount': 3,
        'formatter': 'simple',
    }
    LOGGING['handlers']['debug_file'] = {
        'level': 'DEBUG',
        'class': 'logging.handlers.RotatingFileHandler',
        'filename': os.path.join(LOG_DIR, 'debug.log'),
        'maxBytes': 5 * 1024 * 1024,
        'backupCount': 3,
        'formatter': 'verbose',
    }
    LOGGING['handlers']['stripe'] = {
        'level': 'INFO',
        'class': 'logging.handlers.RotatingFileHandler',
        'filename': os.path.join(LOG_DIR, 'stripe.log'),
        'maxBytes': 5 * 1024 * 1024,
        'backupCount': 3,
        'formatter': 'verbose',
    }
    LOGGING['handlers']['crontab'] = {
        'level': 'INFO',
        'class': 'logging.handlers.RotatingFileHandler',
        'filename': os.path.join(LOG_DIR, 'cron.log'),
        'formatter': 'simple',
    }
    LOGGING['loggers']['django']['handlers'].append('error_file')
    LOGGING['loggers']['stripe']['handlers'].append('stripe')
    LOGGING['loggers']['stripe']['handlers'].append('error_file')
    LOGGING['loggers']['crontab']['handlers'].append('crontab')

# Database configurations
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    },
    'sf_hotspot': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'sf_hotspot.sqlite',
    }
}

# Message tags for Django messages framework
MESSAGE_TAGS = {
    messages.DEBUG: 'alert-info',
    messages.INFO: 'alert-info',
    messages.SUCCESS: 'alert-success',
    messages.WARNING: 'alert-warning',
    messages.ERROR: 'alert-danger',
}

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator'},
    {'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator'},
    {'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator'},
]

# Authentication URLs
LOGIN_URL = 'fiberPlan:login'
LOGOUT_REDIRECT_URL = '/fp/login/?next=/fp/'

# Email settings
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_USE_TLS = True
EMAIL_HOST = 'smtp.office365.com'
EMAIL_HOST_USER = '<EMAIL>'
SERVER_EMAIL = '<EMAIL>'
DEFAULT_FROM_EMAIL = '<EMAIL>'
EMAIL_HOST_PASSWORD = os.getenv('EMAIL_HOST_PASSWORD', 'Vuv20455')
EMAIL_PORT = 587

# Internationalization
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'America/New_York'
USE_I18N = True
USE_TZ = True

# External API keys and constants
GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY', 'AIzaSyBx7wjTw-gBlf1CTIVKYtvx8JsxR9rlQRA')
BASE_COUNTRY = 'US'

# Static and media
STATIC_ROOT = BASE_DIR / 'static'
STATIC_URL = '/static/'
STATICFILES_DIRS = [BASE_DIR / 'templates/static']
GEODATA_ROOT = BASE_DIR / 'templates/static/geodata'
GEODATA_URL = '/geodata/'

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Stripe configuration
STRIPE_MODE = os.getenv('STRIPE_MODE', 'DEMO')
STRIPE_SECRET_KEY_TEST = os.getenv('STRIPE_SECRET_KEY_TEST')
STRIPE_SECRET_KEY_LIVE = os.getenv('STRIPE_SECRET_KEY_LIVE')
STRIPE_SECRET_KEY = STRIPE_SECRET_KEY_TEST if STRIPE_MODE == 'DEMO' else STRIPE_SECRET_KEY_LIVE
STRIPE_ACCOUNT_ID = os.getenv('STRIPE_ACCOUNT_ID')
STRIPE_WEBHOOK_SECRET_TEST = os.getenv('STRIPE_WEBHOOK_SECRET_TEST')
STRIPE_WEBHOOK_SECRET_LIVE = os.getenv('STRIPE_WEBHOOK_SECRET_LIVE')
STRIPE_WEBHOOK_SECRET = STRIPE_WEBHOOK_SECRET_TEST if STRIPE_MODE == 'DEMO' else STRIPE_WEBHOOK_SECRET_LIVE

# Gandalf API configuration
GANDALF_API_KEY = os.getenv('GANDALF_API_KEY')
GANDALF_API_URL = os.getenv('GANDALF_API_URL', 'http://127.0.0.1:8001')