from django.urls import path, include
from django.views.generic import RedirectView
from django.conf.urls.static import static
from django.conf import settings
from django.contrib import admin  # Import the admin module
from django.contrib.auth.views import LogoutView

urlpatterns = [
                  path('fp/', include(("fiberPlan.urls", 'fiberPlan'), namespace="fiberPlan")),
                  path('et/', include(('englishTowers.urls', 'englishTowers'), namespace='englishTowers')),
                  path('uisp/', include(('samUisp.urls', 'samUisp'), namespace='samUisp')),
                  path('ai/', include(('openAI.urls', 'openAI'), namespace='openAI')),
                  path('', RedirectView.as_view(url="/fp/")),
                  path("admin/", admin.site.urls),
                  path('pf/', include('profiles.urls')),  # Correct path inclusion
                  path('logout/', LogoutView.as_view(), name='logout'),
                  path('nc811/', include(('NC811.urls', 'NC811'), namespace='nc811')),
              ] + static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
