API Documentation
===============

JSON Endpoints
-------------

englishTowers
~~~~~~~~~~~~

.. function:: /et/jsonresponse?param=profile_exists&unit=[unit]

   Checks if a security profile exists for a specific unit.

   :param unit: The unit identifier
   :returns: JSON with result indicating if profile exists

.. function:: /et/jsonresponse?param=reset_ap&ipaddress=[ip]

   Resets an access point.

   :param ipaddress: The IP address of the router
   :returns: JSON with result indicating success

.. function:: /et/jsonresponse?param=get_routers

   Gets a list of all routers.

   :returns: JSON array of router information

.. function:: /et/jsonresponse?param=orders

   Gets a list of orders for the current month.

   :returns: JSON array of order information

fiberPlan
~~~~~~~~

.. function:: /fp/jsonresponse

   Various endpoints for fiber planning data.

   :returns: JSON with requested data

hotspot
~~~~~~

.. function:: /hs/jsonresponse

   Endpoints for hotspot data.

   :returns: JSON with requested data