Applications
===========

The project consists of several Django applications:

fiberPlan
---------

The fiberPlan application manages fiber network planning, vaults, and NC811 tickets.

Key features:
- Vault management
- NC811 ticket tracking and management
- Geocoding and distance calculations
- Map visualization

englishTowers
------------

The englishTowers application handles rental networks and router management.

Key features:
- Router management
- Network rental processing
- Payment integration with Stripe
- Order tracking

hotspot
-------

The hotspot application provides analytics and financial reporting.

Key features:
- Charts and visualizations
- Financial reporting

profiles
--------

The profiles application manages user profiles and authentication.

Key features:
- User profile management
- Authentication