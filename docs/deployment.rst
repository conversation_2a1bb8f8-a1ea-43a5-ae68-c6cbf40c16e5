Deployment
==========

Production Deployment
-------------------

The application is deployed using Gun<PERSON> and Nginx on a Linux server.

Deployment Process
~~~~~~~~~~~~~~~~

1. Set up the server environment with required dependencies
2. Clone the repository to /srv/www/django.esvc.us/hotspot
3. Set up a virtual environment and install dependencies
4. Configure environment variables
5. Run migrations and collect static files
6. Configure Gunicorn as a systemd service
7. Configure Nginx as a reverse proxy

Auto Deployment
~~~~~~~~~~~~~

The project includes an auto-deployment script (`auto_deploy.sh`) that:

1. Pulls the latest code from the main branch
2. Runs migrations
3. Collects static files
4. Restarts Gunicorn

Cron Jobs
--------

The application uses django-crontab for scheduled tasks:

- NC811 ticket refresh (hourly)
- Router updates (weekly)
- Rental network updates (daily)
- Customer updates (daily)

To install the cron jobs::

    python manage.py crontab add

To show current cron jobs::

    python manage.py crontab show

To remove all cron jobs::

    python manage.py crontab remove