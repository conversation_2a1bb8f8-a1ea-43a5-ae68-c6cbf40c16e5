Installation
===========

Requirements
-----------

- Python 3.8+
- Django 5.0+
- MySQL/MariaDB
- Additional requirements listed in requirements.txt

Setup
-----

1. Clone the repository::

    git clone https://github.com/yourusername/hotspot.git
    cd hotspot

2. Create and activate a virtual environment::

    python -m venv venv
    source venv/bin/activate  # On Windows: venv\Scripts\activate

3. Install dependencies::

    pip install -r requirements.txt

4. Configure environment variables (create a .env file)::

    DJANGO_SECRET_KEY=your_secret_key
    DJANGO_DEBUG=False
    ALLOWED_HOSTS=legolas.esvc.us,example.com
    EMAIL_HOST_PASSWORD=your_email_password
    GOOGLE_API_KEY=your_google_api_key
    STRIPE_SECRET_KEY=your_stripe_key

5. Run migrations::

    python manage.py migrate

6. Create a superuser::

    python manage.py createsuperuser

7. Run the development server::

    python manage.py runserver