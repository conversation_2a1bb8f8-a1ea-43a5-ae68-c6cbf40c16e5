<?php

namespace HS\FrontendBundle\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Request;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Component\Serializer\Normalizer\PropertyNormalizer;
use HS\FrontendBundle\Form\CardDataType;
use HS\FrontendBundle\Entity\Radreply;
use HS\FrontendBundle\Entity\Radcheck;
use HS\FrontendBundle\Entity\Carddata;
use HS\FrontendBundle\Entity\Orders;
use HS\FrontendBundle\Entity\ETWifi;
use HS\FrontendBundle\Entity\Nas;

class AdminController extends Controller {

    public function adminAction() {
        
        //$speed = 50;
        //$this->setShapingQueue('10.0.30.71',$speed);
        //$this->setGraphing('10.0.30.247');

        $em = $this->getDoctrine()->getManager();

        $date = date('Y-m-d', strtotime("now")) . "%";
        
        //$this->setChannelPlan();

        //$sandbox = $this->getSandbox();
	$sandbox = false;
        $orders=array();
        if ($sandbox) {
            $query = $em->createQuery(
                    "SELECT o
                 FROM HSFrontendBundle:Orders o
                 WHERE o.createdAt LIKE '" . $date . "'
                 ORDER BY o.createdAt DESC");
        } else {
            $query = $em->createQuery(
                    "SELECT o
                 FROM HSFrontendBundle:Orders o
                 WHERE o.createdAt LIKE '" . $date . "' and o.approval NOT LIKE 'TEST%'
                 ORDER BY o.createdAt DESC");
        }
        $orders = $query->getResult();


        /* update IP addresses */
        
        /*
          foreach ($arrUnits as $unit => $ip) {
          //echo 'et'.$unit. ' ip address: '.$ip;
          $qb = $em->createQueryBuilder() ->update('HSFrontendBundle:Nas', 'n') ->set('n.ipaddress', ':par1') ->where('n.shortname = :par2')->setParameter('par1',$ip)->setParameter('par2','et'.$unit);
          $q = $qb->getQuery();  $results = $q->execute();
          echo $unit .'\n';
          }
          foreach ($arrUnits as $unit => $ip) {
          //echo 'et'.$unit. ' ip address: '.$ip;
          $query = $em->createQuery(
          "update HSFrontendBundle:Nas n"
          . "set n.ipaddress = '". $ip ."' where"
          . "n.shortname = 'et'". $unit);
          $query->getResult();
          echo $unit .'\n';
          }
          die();
         * 
         */
        $firstDay = new \Datetime('first day of this month');
        $lastDay = new \DateTime('last day of this month');
        /*
          $query = $em->createQuery(
          "SELECT SUM(o.price)
          FROM HSFrontendBundle:Orders o
          WHERE o.customer = 'engtwrs' AND o.approval NOT LIKE 'TEST%' AND o.createdAt BETWEEN '" .
          $firstDay->format('Y-m-d') . "' AND '" . $lastDay->format('Y-m-d') . "'");

          $total = $query->getResult();

          $query = $em->createQuery(
          "SELECT SUM(o.price)
          FROM HSFrontendBundle:Orders o
          WHERE o.customer = 'kiptopeke' AND o.approval NOT LIKE 'TEST%' AND o.createdAt BETWEEN '" .
          $firstDay->format('Y-m-d') . "' AND '" . $lastDay->format('Y-m-d') . "'");

          $total2 = $query->getResult();

          $total['engtwrs'] = $total[0][1];
          $total['kiptopeke'] = $total2[0][1];

          $total['total'] = $total[0][1]+$total2[0][1];
          $total['royalty'] = ($total[0][1]*0.25)+($total2[0][1]*0.05);
         */
        $query = $em->createQuery(
                "SELECT o.price,o.customer, o.createdAt
                 FROM HSFrontendBundle:Orders o
                 WHERE o.approval NOT LIKE 'TEST%' AND o.createdAt BETWEEN '" .
                $firstDay->format('Y-1-1') . "' AND '" . $lastDay->format('Y-m-d') . "' ORDER BY o.createdAt");
        $total3 = $query->getResult();
        $totals['engtwrs'] = array('ytd' => 0, 'mtd' => 0);
        $totals['kiptopeke'] = array('ytd' => 0, 'mtd' => 0);
        foreach ($total3 as $result) {
            //if($result['created_at']->getTimestamp() > $firstDay->getTimestamp())
            $cust = $result['customer'];
            if ($firstDay->getTimestamp() < $result['createdAt']->getTimestamp()) {
                if (!array_key_exists($cust, $totals))
                    $totals[$cust] = array('ytd' => 0, 'mtd' => 0);
                $totals[$cust]['ytd'] += $result['price'];
                $totals[$cust]['mtd'] += $result['price'];
            } else {
                if (!array_key_exists($cust, $totals))
                    $totals[$cust] = array('ytd' => 0, 'mtd' => 0);
                $totals[$cust]['ytd'] += $result['price'];
            }
        }

        return $this->render('HSFrontendBundle:Admin:admin.html.twig', array('orders' => $orders, 'total' => $totals));
    }
    
    public function channelPlanAction() {
        $this->setChannelPlan();
        return $this->render('HSFrontendBundle:Admin:subnet.html.twig', array('results' => $arrResults));
    }
    
    /*
     * This function scans the *********/23 subnet. It does this by pinging each ip address, and then connecting and reading the router identity for each host reported up.
     * This informaiton is scanned into a two dimensional array with UNIT and IP keys. Once the block has been scanned ip addresses are updated in the database.
     * I would like to turn this into a recurring task so IP addresses are always current. 
     * An alert will be generated with the number of UNITS updated and the time it took to complete (about 21 minutes)
     */
    public function subnetAction() { 
        $arrResults = array();        
        $run = true;
        $login = 'admin';
        $password = '3SHOREn3t';
        if ($run) {
            $time_pre = microtime(true);
            $ip = "10.0.30.";
            for ($i = 2; $i <= 255; $i++) {
                $result = $this->isAlive($ip . $i);
                // echo $ip . $i . " is " . ($result == 0 ? "up" : "down" );
                if ($result == 0) {
                    $unit = $this->getSystemId($ip . $i, $login, $password);
                    if ($unit) {
                        $arrUnits[] = array(
                            'ip' => $ip . $i,
                            'unit' => $unit
                        );
                    };
                    //echo $ip.$i . " is: " . ( $api->isAlive($ip.$i) == 0 ? "UP" : "DOWN" ) . '</br>';
                }
            }
           $ip = "10.0.31.";
           for ($i = 2; $i <= 255; $i++) {
                $result = $this->isAlive($ip . $i);
                // echo $ip . $i . " is " . ($result == 0 ? "up" : "down" );
                if ($result == 0) {
                    $unit = $this->getSystemId($ip . $i, $login, $password);
                    if ($unit) {
                        $arrUnits[] = array(
                            'ip' => $ip . $i,
                            'unit' => $unit
                        );
                    };
                    //echo $ip.$i . " is: " . ( $api->isAlive($ip.$i) == 0 ? "UP" : "DOWN" ) . '</br>';
                }
            }
            $time_post = microtime(true);
            $time = $time_post - $time_pre;;
        }
        $em = $this->getDoctrine()->getManager();
        $index=1;
        foreach($arrUnits as $unit) {
            //echo $index. ' unit: ' . $unit['unit'] . ' has IP Address: ' . $unit['ip'] . '<br />';
            $query = $em->createQuery(
                "SELECT n 
                 FROM HSFrontendBundle:Nas n
                 WHERE n.shortname = 'et" . $unit['unit'] . "'");
            $nas = $query->getResult();

            $nas[0]->setIpaddress($unit['ip']);

            $em->persist($nas[0]);
            $em->flush();
            $index++;            
        }
        $flash = $this->get('braincrafted_bootstrap.flash');
        $flash->alert($index . ' units found and updated in the database. This took ' . $time . ' seconds');
        return $this->render('HSFrontendBundle:Admin:subnet.html.twig', array('results' => $arrResults));
    }
    /*
     * this function will set a non-overlapping channel plan for all AP's at English Towers.
     * There are three channels for the 2.4Ghz band and four channels for the 5Ghz band
     * wlan1 is 2.4Ghz
     * wlan2 is 5Ghz
     * 
     * impacted Mikrotik fields:    frequency and band=5ghz-a/n
     * /interface wireless
     * 
     */
    public function setChannelPlan() {
        $api = $this->get('routeros');
        $api->debug = false;
        $login = "admin";
        $password = "3SHOREn3t";
        $em = $this->getDoctrine()->getManager();
        $query = $em->createQuery(
                "SELECT n 
                 FROM HSFrontendBundle:Nas n");
        $routers = $query->getResult();
        $arr24 = array("A"=> "2412", "B" => "2437", "C" => "2462");
        $arr5 = array("A" => "5765", "B" => "5785", "C" => "5180", "D" => "5220","E" => "5200" );
        $channels = array(array('101','A','C'),array('102','B','E'),array('103','C','E'),array('104','A','B'),array('105','B','D'),array('106','C','A'),array('107','B','B'),array('108','A','D'),array('201','B','D'),array('202','A','B'),array('203','B','B'),array('204','C','A'),array('205','A','C'),array('206','B','E'),array('207','A','D'),array('208','C','C'),array('301','A','A'),array('302','B','C'),array('303','A','C'),array('304','B','E'),array('305','C','B'),array('306','A','D'),array('307','C','A'),array('308','B','E'),array('401','C','E'),array('402','A','B'),array('403','C','D'),array('404','A','B'),array('405','B','A'),array('406','C','E'),array('407','B','C'),array('408','A','A'),array('501','B','D'),array('502','C','C'),array('503','B','E'),array('504','C','A'),array('505','A','B'),array('506','B','A'),array('507','A','D'),array('508','C','B'),array('601','C','E'),array('602','A','B'),array('603','C','D'),array('604','A','B'),array('605','B','C'),array('606','C','D'),array('607','B','E'),array('608','A','A'),array('701','A','A'),array('702','B','C'),array('703','A','C'),array('704','B','D'),array('705','C','B'),array('706','A','E'),array('707','C','A'),array('708','B','B'),array('801','C','D'),array('802','A','B'),array('803','C','E'),array('804','A','B'),array('805','B','C'),array('806','C','A'),array('807','B','D'),array('808','A','E'),array('901','B','E'),array('902','C','C'),array('903','B','C'),array('904','C','A'),array('905','A','D'),array('906','B','B'),array('907','A','E'),array('908','C','D'),array('1001','C','A'),array('1002','A','E'),array('1003','B','D'),array('1004','A','B'),array('1005','B','C'),array('1006','C','D'),array('1007','B','B'),array('1008','A','A'),array('1101','A','D'),array('1102','C','B'),array('1103','C','A'),array('1104','B','C'),array('1105','C','E'),array('1106','B','A'),array('1107','A','C'),array('1108','C','E'),array('1201','B','B'),array('1202','A','C'),array('1203','A','C'),array('1204','C','A'),array('1205','A','D'),array('1206','C','E'),array('1207','B','D'),array('1208','A','B'),array('1401','C','A'),array('1402','B','D'),array('1403','B','B'),array('1404','A','E'),array('1405','B','C'),array('1406','A','B'),array('1407','C','A'),array('1408','B','E'),array('1501','B','B'),array('1502','C','A'),array('1503','A','A'),array('1504','C','D'),array('1505','A','E'),array('1506','B','C'),array('1507','A','D'),array('1508','C','B'),array('1601','A','C'),array('1602','B','D'),array('1603','C','D'),array('1604','B','A'),array('1605','C','B'),array('1606','A','E'),array('1607','C','C'),array('1608','B','A')) ;
                
        foreach($routers as $router ) {
            echo $router->getShortname() . ', ';
            foreach($channels as $channel) {
                if('et'.$channel[0] == $router->getShortname() ) {
                    //echo $router->getIpaddress() . ' 2.4Ghz channel: ' . $arr24[$channel[1]] . ' 5Ghz channel: ' . $arr5[$channel[2]] .  '<br />';
                    //$this->setFrequency($router->getIpaddress(), $arr24[$channel[1]], $arr5[$channel[2]]);
                    $speed = 50;
                    //$this->setShapingQueue($router->getIpaddress(),$speed);
                    $this->setGraphing($router->getIpaddress());
                }
            }
        }
        
        //$this->setFrequency($ip, $chanA, $chan5A);
        
        $flash = $this->get('braincrafted_bootstrap.flash');
        $flash->alert('Frequencies have been set');
        return true;
        return $this->render('HSFrontendBundle:Admin:subnet.html.twig', array('results' => $arrResults));
    }

    public function getActiveNeworks() {
        $em = $this->getDoctrine()->getManager();
        $query = $em->createQuery(
                'SELECT s.nasid, s.passwd, s.speed, s.days, s.createdAt , n.shortname
                  FROM HSFrontendBundle:ETWifi s
                  INNER JOIN HSFrontendBundle:Nas n
                  WHERE s.nasid = n.id');
        // $results = $this->getDoctrine()->getRepository('HSFrontendBundle:ETWifi')->findBy(array(),array('createdAt' => 'DESC'));
        // $nas = $this->getDoctrine()->getRepository('HSFrontendBundle:Nas')->findAll();
        try {
            //$results = $query->getArrayResult();
            $results = $query->getResult();
            //print_r($results);die();
        } catch (\Doctrine\ORM\NoResultException $e) {
            $results = array();
        }
        $active = array();

        foreach ($results as $result) {
            // determine if there is time left on the account
            $now = time();
            $created = $result['createdAt']->format('Y-m-d H:i:s');

            $expires = date('D M jS \a\t H:i:s', (strtotime($created) + $result['days'] * 24 * 60 * 60));
            $timeleft = (strtotime($created) + $result['days'] * 24 * 60 * 60 ) - $now;        // number of seconds left
            // echo "Unit: ".$result->getUnit().' CA: '.$created.'" +'.$result->getDays().' days" Expires: '.$expires." timeleft: ".$timeleft. "<br />";

            if ($timeleft > 0) {
                $active[] = array(
                    'unit' => $result['shortname'],
                    'password' => $result['passwd'],
                    'speed' => $result['speed'],
                    'days' => $result['days'],
                    'expires' => $expires
                );
            }
        }
        return $active;
    }

    public function waplookupAction(Request $request) {
        if (!$request->isMethod("POST")) {
            $route = $this->generateUrl('hs_frontend_admin');
            return $this->redirect($route);              
        }
        $flash = $this->get('braincrafted_bootstrap.flash');
        $action = $request->request->get('action');
        $unit = $request->request->get('unit');
        // get unit data
        $em = $this->getDoctrine()->getManager();
        $query = $em->createQuery(
                "SELECT n 
                 FROM HSFrontendBundle:Nas n
                 WHERE n.shortname = 'ldgap0" . $unit . "' OR n.shortname = 'et" . $unit . "'");
        $nas = $query->getResult();
        if (empty($nas)) {
            $flash->alert('Unit ' . $unit . ' does not exist.');
            $route = $this->generateUrl('hs_frontend_admin');
            return $this->redirect($route);
        }
        switch ($action) {
            case "CLEAN AP":
                $flash->alert('ACTION: ' . $action);
                $api = $this->get('routeros');
                $login = "admin";
                $password = "3SHOREn3t";
                $net['ip'] = $nas[0]->getIpaddress();
                $net['customer'] = $nas[0]->getCommunity();
                $result = $api->resetAP($net, $login, $password);
                // remove network from ETWifi table
                $query = $em->createQuery(
                        "DELETE FROM HSFrontendBundle:ETWifi e
                         WHERE e.nasid = " . $nas[0]->getId()
                );
                $runquery = $query->getResult();
                $route = $this->generateUrl('hs_frontend_admin');
                return $this->redirect($route);
                break;
            case "GO":
                $route = $this->generateUrl('hs_frontend_wapstatus', array('unit' => $nas[0]->getShortname()));
                return $this->redirect($route);
                break;
            default:
                $route = $this->generateUrl('hs_frontend_admin');
                return $this->redirect($route);                
                break;
        }
    }

    public function devicesAction(Request $request) {
        $em = $this->getDoctrine()->getManager();
        $query = $em->createQuery(
                "SELECT n 
                 FROM HSFrontendBundle:Nas n
                 WHERE n.community = 'engtwrs' ORDER BY n.shortname ASC");
        $nas = $query->getResult();
        return $this->render('HSFrontendBundle:Admin:devices.html.twig', array('data' => $nas));
    }

    public function wapeditAction(Request $request, $unit) {
        if (empty($unit)) {
            $route = $this->generateUrl('hs_frontend_admin');
            return $this->redirect($route);
        }
        $flash = $this->get('braincrafted_bootstrap.flash');
        // get unit data
        $em = $this->getDoctrine()->getManager();
        $query = $em->createQuery(
                "SELECT n 
                 FROM HSFrontendBundle:Nas n
                 WHERE n.shortname = '" . $unit . "'");
        $nas = $query->getResult();
        if (empty($nas)) {
            $flash->alert('Unit ' . $unit . ' does not exist.');
            $route = $this->generateUrl('hs_frontend_admin');
            return $this->redirect($route);
        }
        return $this->render('HSFrontendBundle:Admin:wapedit.html.twig', array('data' => $nas));
    }

    public function wapsaveAction(Request $request) {
        // provision English Towers AP
        if (!$request->isMethod("POST")) {
            $route = $this->generateUrl('hs_frontend_devices');
            return $this->redirect($route);            
        }
        $flash = $this->get('braincrafted_bootstrap.flash');
        $em = $this->getDoctrine()->getManager();
        // we got some information through a POST
        $unit = trim($request->request->get('unit'));
        $ipaddress = $request->request->get('ipaddress');
        $description = $request->request->get('description');
        // check IP Address
        if (!filter_var($ipaddress, FILTER_VALIDATE_IP)) {
            $flash->alert('Please enter a valid ip address');
            $route = $this->generateUrl('hs_frontend_wapedit', array('unit' => $unit));
            return $this->redirect($route);
        }
        // $flash->alert('Unit: '.$unit.' IP: '.$ipaddress . ' Note: '.$description);
        // check if IP address is unique
        $query = $em->createQuery(
                "SELECT n 
                 FROM HSFrontendBundle:Nas n
                 WHERE n.ipaddress = '" . $ipaddress . "'");
        $nas = $query->getResult();

        if (!empty($nas)) {
            if ($nas[0]->getShortname() <> $unit) {
                $flash->alert('IP Address already assigned to unit: ' . $nas[0]->getShortname());
                $route = $this->generateUrl('hs_frontend_wapedit', array('unit' => $unit));
                return $this->redirect($route);
            }
        }

        // save changes
        $query = $em->createQuery(
                "SELECT n 
                 FROM HSFrontendBundle:Nas n
                 WHERE n.shortname = '" . $unit . "'");
        $nas = $query->getResult();

        $nas[0]->setIpaddress($ipaddress);
        $nas[0]->setDescription($description);

        $em->persist($nas[0]);
        $em->flush();

        $flash->alert('Unit ' . $unit . ' updated.');

        return $this->render('HSFrontendBundle:Admin:wapedit.html.twig', array('data' => $nas));
    }

    public function wapstatusAction(Request $request, $unit) {
        if (empty($unit)) {
            
            $route = $this->generateUrl('hs_frontend_admin');
            return $this->redirect($route);
        }
        $flash = $this->get('braincrafted_bootstrap.flash');
        
        // get unit data
        $em = $this->getDoctrine()->getManager();
        $query = $em->createQuery(
                "SELECT n 
                 FROM HSFrontendBundle:Nas n
                 WHERE n.shortname = '" . $unit . "'");
        $nas = $query->getResult();
        if (empty($nas)) {
            $flash->alert('Unit ' . $unit . ' does not exist.');
            $route = $this->generateUrl('hs_frontend_admin');
            return $this->redirect($route);
        }
        /* ping IP address, return w/error if ping timeout */                
        if($this->isAlive( $nas[0]->getIpAddress()) <> 0 ) {
            $flash->alert('Unit ' . $unit . ' with ip address '. $nas[0]->getIpaddress() .' is offline or ip address changed');
            $route = $this->generateUrl('hs_frontend_admin');
            return $this->redirect($route);
        }
        /* get UNIT ID and compare against database */
        $unitID = $this->getSystemId( $nas[0]->getIpaddress() );
        if($unitID <> substr($unit,2)) {
            $flash->alert('Error:  ' . substr($unit,2). ' not found. Check IP Address, and Restart Router if possible');
            $route = $this->generateUrl('hs_frontend_admin');
            return $this->redirect($route);
        }
         /* get recent orders for this unit */
        $query = $em->createQuery(
                "SELECT n 
                FROM HSFrontendBundle:Orders n
                WHERE n.notes = '" . substr($unit, 2) . "' and n.createdAt > '2018-01-01'");
        $rev = $query->getResult();
        $data = array();
        $data[$nas[0]->getShortname()] = array(
            'unit' => $nas[0]->getShortname(),
            'customer' => $nas[0]->getCommunity(),
            'ipaddress' => $nas[0]->getIpaddress(),
            'description' => $nas[0]->getDescription()
        );
        $data["orderTotal"] = 0;
        $data["orders"] = null;
        foreach ($rev as $order) {
            $data["orders"][] = array("transactionid" => $order->getTransactionid(), "price" => $order->getPrice(), "created_at" => $order->getCreatedat());
            $data["orderTotal"] += $order->getPrice();
        }
        #dump($data["orders"]);die();
        $data[$nas[0]->getShortname()]['hotspot'] = $this->getHotspot($nas[0]->getIpaddress());
        $retval = $this->getWifiNetworks($nas[0]->getIpaddress());
        $data[$nas[0]->getShortname()]['wifi'] = $retval['wap'];
        $data[$nas[0]->getShortname()]['clients'] = $retval['registrationlist'];
        //print_r($data);die();

        return $this->render('HSFrontendBundle:Admin:wapstatus.html.twig', array('data' => $data[$nas[0]->getShortname()], 'orders' => $data["orders"], 'orderTotal' => $data["orderTotal"]));
    }

    public function newrentalAction(Request $request) {
        // provision English Towers AP
        if (!$request->isMethod("POST")) {
            $route = $this->generateUrl('hs_frontend_admin');
            return $this->redirect($route);
        }
        $flash = $this->get('braincrafted_bootstrap.flash');
        $em = $this->getDoctrine()->getManager();
        // we got some information through a POST
        $unit = $request->request->get('unit');
        $passwd = '2024' . $request->request->get('password');
        $days = $request->request->get('days');
        $speed = $request->request->get('speed');
        $customer = $request->request->get('customer');

        // check if account already exists

        $active = $this->getActiveNeworks();
        $foundunit = false;
        foreach ($active as $activeAr) {
            if ($activeAr['unit'] == 'et' . $unit || $activeAr['unit'] == 'ldgap0' . $unit) {
                $foundunit = true;
                break;
            }
        }

        switch ($customer) {
            case "kiptopeke":
                $query = $em->createQuery(
                        "SELECT n 
                 FROM HSFrontendBundle:Nas n
                 WHERE n.shortname = 'ldgap0" . $unit . "'");
                $ssid = "Rental0" . $unit;
                break;
            case "engtwrs":
                $query = $em->createQuery(
                        "SELECT n 
                 FROM HSFrontendBundle:Nas n
                 WHERE n.shortname = 'et" . $unit . "'");
                $ssid = "Rental" . $unit;
                break;
        }

        $nas = $query->getResult();
        if (empty($nas)) {
            $flash->alert('Unit does not exist or not found.');
            $route = $this->generateUrl('hs_frontend_admin');
            return $this->redirect($route);
        }
        $api = $this->get('routeros');
        $login = "admin";
        $password = "3SHOREn3t";

        $api->debug = false;

        if ($api->connect($nas[0]->getIpaddress(), $login, $password)) {

            if ($foundunit) {
                $passwordreset = $this->updateMikrotikWAP($api, $unit, $passwd); // Returns true/false if the password could be updated.
                if ($passwordreset) {
                    $etwifi = $em->getRepository('HSFrontendBundle:ETWifi')->findOneByNasid($nas[0]->getId());
                    $etwifi->setPasswd($passwd);
                    $flash->info('Password has been set to: ' . $passwd);
                } else {
                    $flash->error('Unable to change password');
                    $route = $this->generateUrl('hs_frontend_admin');
            return $this->redirect($route);
                }
            } else {
                $net['ip'] = $nas[0]->getIpaddress();
                $net['customer'] = $nas[0]->getCommunity();
                //$result = $api->resetAP($net,$login,$password);
                $newWAP = $this->newMikrotikWAP($api, $unit, $passwd, $customer);
                $etwifi = new ETWifi();
                $etwifi->setNasid($nas[0]->getId());
                $etwifi->setPasswd($passwd);
                $etwifi->setSpeed($speed);
                $etwifi->setDays($days);
                $etwifi->setCreatedAt(new \DateTime('now'));
            }
            $api->disconnect();

            try {
                $em->persist($etwifi);
            } catch (\Exception $e) {
                switch (get_class($e)) {
                    case 'Doctrine\DBAL\DBALException':
                        echo "DBAL Exception<br />";
                        print_r($etwifi);
                        $logger = $this->get('logger');
                        $logger->error('Could not write record');
                        break;
                    case 'Doctrine\DBA\DBAException':
                        echo "DBA Exception<br />";
                        print_r($etwifi);
                        $logger = $this->get('logger');
                        $logger->error('Could not write record');
                        break;
                    default:
                        throw $e;
                        print_r($etwifi);
                        $logger = $this->get('logger');
                        $logger->error('Could not write record');
                        break;
                }
            }
            $em->flush();

            $flash->alert('Wireless Network created and activated');
        } else {
            $flash = $this->get('braincrafted_bootstrap.flash');
            $flash->error('Could not create wireless network.');
        }
        $route = $this->generateUrl('hs_frontend_admin');
            return $this->redirect($route);
    }

    public function updateMikrotikWAP($api, $unit, $passwd) {
        // step 1: add a security profile
        // check if security profile exists, if so change the password
        $api->write('/interface/wireless/security-profiles/print');
        $read = $api->read(false);
        $results = $api->parseResponse($read);
        $id = 0;
        $found = false;
        foreach ($results as $result) {
            //print_r($result);echo "<br />";
            if ($result['name'] == "Rental") {
                $api->comm('/interface/wireless/security-profiles/set', array(
                    ".id" => $result['.id'],
                    "wpa-pre-shared-key" => $passwd,
                    "wpa2-pre-shared-key" => $passwd,
                ));
                $found = true;
            }
            $id++;
        }
        $api->disconnect();
        return $found;
    }

    public function getHotspot($ip) {
        $api = $this->get('routeros');
        $login = "admin";
        $password = "3SHOREn3t";
        $found = "OFF";
        $api->debug = false;
        if ($api->connect($ip, $login, $password)) {
            $api->write('/ip/hotspot/print');
            $read = $api->read(false);
            $results = $api->parseResponse($read);
            $id = 0;
            foreach ($results as $result) {
                if ($result['name'] == "hotspot1") {
                    $found = ($result['disabled'] == "true") ? "DISABLED" : "ON";
                    break;
                }
            }
        }
        $api->disconnect();
        return $found;
    }
    
    public function setGraphing($ip){
        $api = $this->get('routeros');
        $login = "admin";
        $password = "3SHOREn3t";
        $api->debug = false;
        if ($api->connect($ip, $login, $password)) {
             //step 1: activate graphing for all interfaces            
            $api->comm('/tool/graphing/add',array(
               'allow-address' => '0.0.0.0/0',
               'interface' => 'ALL',
               'store-on-disk' => 'yes'
            ));
            // add a filter rule allowing incoming port 80
            $api->comm('/ip/firewall/filter/add',array(
               'chain' => 'input',
               'action' => 'accept',
               'protocol' => 'tcp',
                'dst-port' => '80',
                'place-before' => '3'
            ));
        }
        $api->disconnect();
        return true;
    }
    
    public function setShapingQueue($ip, $speed){
        $api = $this->get('routeros');
        $login = "admin";
        $password = "3SHOREn3t";
        $api->debug = false;
        if ($api->connect($ip, $login, $password)) {
             //step 1: remove simple queues
            $api->write('/queue/simple/print');
            $read = $api->read(false);
            $results = $api->parseResponse($read);            
            foreach ($results as $result) {                
                $id = $result['.id'];
                $api->comm('/queue/simple/remove', array(
                    '.id' => $id
                ));                
            }
            // step 2: write new queue
            $burstLimit = $speed * 1.1;
            $maxLimit = $speed;
            $burstThreshold = $speed;
            $burstTime = 5;
            $api->comm('/queue/simple/add',array(
               'name' => 'Owner 50Mb 050121',
               'queue' => 'ethernet-default/ethernet-default',
               'target' => '************/24',
               'dst' => 'ether1',
               'max-limit' => $speed.'M/'.$speed.'M',
               'burst-limit' => $burstLimit.'M/'.$burstLimit.'M',
               'burst-threshold' => $burstThreshold.'M/'.$burstThreshold.'M',
               'burst-time' => $burstTime.'/'.$burstTime
            ));            
        }
        $api->disconnect();
        return true;
    }
    
    public function setFrequency($ip, $freq2, $freq5){
        $api = $this->get('routeros');
        $login = "admin";
        $password = "3SHOREn3t";
        $api->debug = false;
        if ($api->connect($ip, $login, $password)) {
            $api->write('/interface/wireless/print');
            $read = $api->read(false);
            $results = $api->parseResponse($read);
            foreach ($results as $result) {
                    switch ($result['name']) {
                        case "wlan1":
                            $api->comm('/interface/wireless/set', array(
                                ".id" => $result['.id'],
                                "frequency" => $freq2,
                            ));
                            break;
                        case "wlan2";
                            $api->comm('/interface/wireless/set', array(
                                ".id" => $result['.id'],
                                "frequency" => $freq5,
                                "band" => "5ghz-a/n",
                                "channel-width" => "20/40mhz-Ce",
                            ));
                            break;
                    }                
            }
        }
        $api->disconnect();
        return true;
    }

    public function toggleHotspot($ip) {
        $api = $this->get('routeros');
        $login = "admin";
        $password = "3SHOREn3t";
        // $api->debug = true;
        if ($api->connect($ip, $login, $password)) {
            $api->write('/ip/hotspot/print');
            $read = $api->read(false);
            $results = $api->parseResponse($read);
            foreach ($results as $result) {
                if ($result['name'] == "hotspot1") {
                    switch ($result['disabled']) {
                        case "true":
                            $api->comm('/ip/hotspot/set', array(
                                ".id" => $result['.id'],
                                "disabled" => "false",
                            ));
                            break;
                        case "false";
                            $api->comm('/ip/hotspot/set', array(
                                ".id" => $result['.id'],
                                "disabled" => "true",
                            ));
                            break;
                    }
                }
            }
        }
        $api->disconnect();
        return true;
    }

    public function getWifiNetworks($ip) {
        $api = $this->get('routeros');
        $api->debug = false;
        $login = "admin";
        $password = "3SHOREn3t";
        $data = array();
        $arclient = array();
        if ($api->connect($ip, $login, $password)) {
            $waps = $api->comm('/interface/wireless/getall');
            $api->write('/interface/wireless/registration-table/print', false);
            $api->write('=stats=');
            $read = $api->read(false);
            $clients = $api->parseResponse($read);
            foreach ($waps as $wap) {
                $profiles = $api->comm('/interface/wireless/security-profiles/print');
                foreach ($profiles as $profile) {
                    if ($profile["name"] == $wap["security-profile"]) {
                        if ($profile['mode'] == "none") {
                            $data["wap"][$wap["name"]] = array("profile" => $wap['security-profile'], "ssid" => $wap['ssid'], "password" => "open");
                        } else {
                            $data["wap"][$wap["name"]] = array("profile" => $wap['security-profile'], "ssid" => $wap['ssid'], "password" => $profile["wpa2-pre-shared-key"]);
                        }
                    }
                }
            }
            foreach ($clients as $client) {
                array_push($arclient, array("wap" => $client["interface"], "macaddress" => $client["mac-address"], "uptime" => $client["uptime"], "ipaddress" => $client["last-ip"]));
            }
            $data["registrationlist"] = $arclient;
        }
        $api->disconnect();
        return $data;
    }

    public function toggleHsAction($unit) {
        $em = $this->getDoctrine()->getManager();
        $nas = $em->getRepository('HSFrontendBundle:Nas')->findOneByShortname($unit);
        $this->toggleHotspot($nas->getIpaddress());
        $flash = $this->get('braincrafted_bootstrap.flash');
        $flash->info('Hotspot status updated');
        $route = $this->generateUrl('hs_frontend_wapstatus', array('unit' => $unit));
        return $this->redirect($route);        
    }

    public function resetMikrotikWAP($api) {
        // remove virtual ap from bridge
        // remove rental wifi
        // remove security profile        
        // 
        // step 1: add a security profile
        // check if security profile exists, if so change the password
        $api->write('/interface/wireless/security-profiles/print');
        $read = $api->read(false);
        $results = $api->parseResponse($read);
        $id = 0;
        $found = false;
        foreach ($results as $result) {
            //print_r($result);echo "<br />";
            if ($result['name'] == "Rental") {
                $api->comm('/interface/wireless/security-profiles/set', array(
                    ".id" => "*" . $id,
                    "wpa-pre-shared-key" => $passwd,
                    "wpa2-pre-shared-key" => $passwd,
                ));
                $found = true;
            }
            $id++;
        }
        $api->disconnect();
        return $found;
    }

    public function newMikrotikWAP($api, $unit, $passwd, $customer) {
        // step 1: add a security profile
        // check if security profile exists, if so change the password
        $api->write('/interface/wireless/security-profiles/print');
        $read = $api->read(false);
        $results = $api->parseResponse($read);
        $id = 0;
        $found = false;
        foreach ($results as $result) {
            //print_r($result);echo "<br />";
            if ($result['name'] == "Rental") {
                $api->comm('/interface/wireless/security-profiles/set', array(
                    ".id" => "*" . $id,
                    "wpa-pre-shared-key" => $passwd,
                    "wpa2-pre-shared-key" => $passwd,
                ));
                $found = true;
            }
            $id++;
        }
        if (!$found) {
            $api->comm('/interface/wireless/security-profiles/add', array(
                "name" => "Rental",
                "mode" => "dynamic-keys",
                "wpa-pre-shared-key" => $passwd,
                "wpa2-pre-shared-key" => $passwd,
                "authentication-types" => "wpa-psk,wpa2-psk")
            );
        }
        // step 2 add the virtual wireless interface (2G)
        $api->comm('/interface/wireless/add', array(
            "name" => "Rental",
            "master-interface" => "wlan1",
            "mode" => "ap-bridge",
            "ssid" => "Rental" . $unit,
            "security-profile" => "Rental")
        );
        // step 3 add the virtual wireless interface (5G)
        $api->comm('/interface/wireless/add', array(
            "name" => "Rental5G",
            "master-interface" => "wlan2",
            "mode" => "ap-bridge",
            "ssid" => "Rental" . $unit,
            "security-profile" => "Rental")
        );

        // step 3 activate the interface
        $api->comm('/interface/wireless/enable', array(
            "numbers" => "Rental")
        );
        $api->comm('/interface/wireless/enable', array(
            "numbers" => "Rental5G")
        );
        // step 4: add the wireless interface to the local bridge
        $api->comm('/interface/bridge/port/add', array(
            "bridge" => "bridge-local",
            "interface" => "Rental")
        );
        $api->comm('/interface/bridge/port/add', array(
            "bridge" => "bridge",
            "interface" => "Rental")
        );
        $api->comm('/interface/bridge/port/add', array(
            "bridge" => "bridge-local",
            "interface" => "Rental5G")
        );
        $api->comm('/interface/bridge/port/add', array(
            "bridge" => "bridge",
            "interface" => "Rental5G")
        );
        // OPTIONAL step 5: disable hotspot
        if ($customer == "kiptopeke") {
            $api->write('/ip/hotspot/print');
            $read = $api->read(false);
            $results = $api->parseResponse($read);
            foreach ($results as $result) {
                if ($result['name'] == "hotspot1") {
                    $api->comm('/ip/hotspot/set', array(
                        ".id" => $result['.id'],
                        "disabled" => "true",
                    ));
                }
            }
        }
    }

    /*
     * Credit card data is posted to this function
     * return to the credit card page upon completion
     */

    public function processorderAction(Request $request) {
        //print_r(new \DateTime());die();

        if (!$request->isMethod('POST')) {
            $route = $this->generateUrl('hs_frontend_order');
            return $this->redirect($route);            
        }

        $totalAmount = str_replace('.', '', $request->request->get('totalamount'));
        $unit = $request->request->get('unit');

        $card = new Creditcard($this->container->getParameter('sandbox'));
        $card->setCardNum($request->request->get('ccnumber'));
        $card->setCVV2($request->request->get('cvv'));
        $card->setCardExpMonth($request->request->get('month'));
        $card->setCardExpYear($request->request->get('year'));
        $card->setBillingPostalCode($request->request->get('zip'));
        $card->setTotalAmount($totalAmount);
        $card->setUnit($unit);

        $result = $card->runCard();
        
        /* $logger = $this->get('logger');
          $logger->info(print_r($result));
         */
        if ($result['ActionCode'] == '000') {
            // success
            // store transaction and display results
            $order = new Orders();

            $order->setTransactionID($result['TransactionID']);
            $order->setApproval($result['Approval']);
            $order->setPackage('na');
            $order->setPrice($request->request->get('totalamount'));
            $order->setCustomer('engtwrs');
            $order->setCallingStationId('na');
            $order->setPhoneNumber('na');
            $order->setCreatedAt(new \DateTime());
            $order->setNote($unit);
            $order->setCcnum($request->request->get('ccnumber'));
            $order->setMm($request->request->get('month'));
            $order->setYy($request->request->get('year'));

            $em = $this->getDoctrine()->getManager();
            try {
                $em->persist($order);
            } catch (\Doctrine\ORM\ORMException $e) {
                $logger = $this->get('logger');
                $logger->error("Could not save record to database.");
            }
            $em->flush();
            // set notification, clear card data and render order page.
            $flash = $this->get('braincrafted_bootstrap.flash');
            $flash->alert('Credit Card Transaction was ' . $result['ResponseText']);
            $route = $this->generateUrl('hs_frontend_admin');
            return $this->redirect($route);
        } else {
            // more things gone wrong
            switch ($result['ActionCode']) {
                // visa mastercard
                case '005':
                    $xmlArray['ErrMsg'] = "Card has been Declined! Please try a different card or reenter your information.";
                    break;
                case '051':
                    $xmlArray['ErrMsg'] = "Insufficient funds. Please try a different card.";
                    break;
                case '054':
                    $xmlArray['ErrMsg'] = "Expired Card. Please try a different card.";
                    break;
                case '806';
                    $xmlArray['ErrMsg'] = "CVV code Failure. Please try a different card or reenter your information.";
                    break;
                // American Express
                case '100':
                    $xmlArray['ErrMsg'] = "Card has been Declined. Please try a different card or reenter your information!";
                    break;
                case '110':
                    $xmlArray['ErrMsg'] = "Invalid amount. Please try a different card.";
                    break;
                case '101':
                    $xmlArray['ErrMsg'] = "Expired Card. Please try a different card.";
                    break;
                default:
                    $xmlArray['ErrMsg'] = "Card has been Declined. Please try a different card or reenter your information.";
            }
            // credit card did not go through.------------------------------ 
            // create error and go back to credit card page
            $flash = $this->get('braincrafted_bootstrap.flash');
            $flash->error('Credit Card Error: ' . $xmlArray['ErrMsg']);
            $route = $this->generateUrl('hs_frontend_order');
            return $this->redirect($route);
        }
    }

    public function transactionsAction($date, Request $request) {
        // standard Symfony functionality
        // check https://symfony.com/doc/current/book/security.html for code and examples
        // admin/3SHOREn3t

        $sandbox = $this->getSandbox();
        //echo ($sandbox)?"SANDBOX":"PRODUCTION";
        
        switch ($date) {
            case "month":
                $date = date('Y-m', strtotime("now"));
                $hdrDate = date('F', strtotime("now"));
                break;
            case "lastmonth":
                $date = date('Y-m', strtotime("first day of last month"));
                $hdrDate = date('F', strtotime("first day of last month"));
                break;
            case "today":
                $date = date('Y-m-d', strtotime("now"));
                $hdrDate = date('l F jS Y', strtotime("now"));
                break;
            default:
                $date = date('Y-m', strtotime($date));
                $hdrDate = date('F', strtotime("now"));
                break;
        }

        $em = $this->getDoctrine()->getManager();
        if (empty($date))
            $date = date('l F jS Y', strtotime("now"));

        if ($sandbox) {
            $query = $em->createQuery(
                    "SELECT o
                 FROM HSFrontendBundle:Orders o
                 WHERE o.createdAt LIKE '" . $date . "%'
                 ORDER BY o.createdAt DESC");
        } else {
            $query = $em->createQuery(
                    "SELECT o
                 FROM HSFrontendBundle:Orders o
                 WHERE o.createdAt LIKE '" . $date . "%' and o.approval NOT LIKE 'TEST%'
                 ORDER BY o.createdAt DESC");
        }
        $orders = $query->getResult();

        return $this->render('HSFrontendBundle:Admin:transactions.html.twig', array('orders' => $orders, 'date' => $date));
    }

    public function creditAction($transid, Request $request) {

        if (empty($transid)) {
            $route = $this->generateUrl('hs_frontend_admin');
            return $this->redirect($route);
        }
        $sandbox = $this->getSandbox();

        $em = $this->getDoctrine()->getManager();

        if (!$request->isMethod("POST")) {
            $order = $this->getDoctrine()->getRepository('HSFrontendBundle:Orders')->findOneByTransactionid($transid);
            $data = array(
                "ccnum" => "************" . substr($order->getCcnum(), -4),
                "expmonth" => $order->getMm(),
                "expyear" => $order->getYy(),
                "approval" => $order->getApproval(),
                "amount" => $order->getPrice(),
                "customer" => $order->getCustomer(),
                "transactionid" => $transid
            );
            $settledate = $order->getCreatedAt()->format('Y-m-d H:i:s');
            $today = date('Y-m-d 00:00:00', strtotime('now'));
            if (strtotime($settledate) < strtotime($today))
                return $this->render('HSFrontendBundle:Admin:credit.html.twig', array('data' => $data, 'sandbox' => $sandbox));
            return $this->render('HSFrontendBundle:Admin:void.html.twig', array('data' => $data, 'sandbox' => $sandbox));
        } else {
            $em = $this->getDoctrine()->getManager();

            $totalAmount = str_replace('.', '', $request->request->get('totalamount'));
            // remove the negative
            $totalAmount = str_replace('-', '', $totalAmount);
            //
            // action = credit if transaction has been settled, which happens at 11pm on the day of the transaction.
            //            
            $action = $request->request->get('action');

            $order = $this->getDoctrine()->getRepository('HSFrontendBundle:Orders')->findOneByTransactionid($transid);

            $card = new Creditcard($this->container->getParameter('sandbox'));

            $card->setCardNum($order->getCcnum());
            $card->setCardExpMonth($request->request->get('month'));
            $card->setCardExpYear($request->request->get('year'));
            $card->setTotalAmount($totalAmount);
            $card->setApproval($request->request->get('approval'));

            $action = 'credit';
            if ($action == 'void') {
                $result = $card->voidRequest($transid, $sandbox);
            } else {
                $result = $card->creditRequest($sandbox);
            }
            //$logger = $this->get('logger');
            //$logger->info(print_r($result));

            if ($result['ActionCode'] == '000') {
                // success credit has been applied
                // store transaction and display result
                $order = new Orders();

                $order->setTransactionID($result['TransactionID']);
                $order->setApproval($result['Approval']);
                $order->setPackage('na');
                $order->setPrice('-' . $request->request->get('totalamount'));
                $order->setCustomer($request->request->get('customer'));
                $order->setCcnum(substr($card->getCardNum(), -4));
                $order->setCallingStationId('na');
                $order->setPhoneNumber('na');
                $order->setNote($request->request->get('note'));
                $order->setCreatedAt(new \DateTime('now'));
                $order->setMm($request->request->get('month'));
                $order->setYy($request->request->get('year'));

                try {
                    $em->persist($order);
                } catch (\Doctrine\ORM\ORMException $e) {
                    $logger = $this->get('logger');
                    $logger->error("Could not save record to database.");
                }
                $em->flush();

                // set notification, clear card data and render order page.
                $flash = $this->get('braincrafted_bootstrap.flash');
                $flash->alert($result['ResponseText']);

                $route = $this->generateUrl('hs_frontend_admin');
                return $this->redirect($route);
            }
            // more things gone wrong            
            $credit = false;
            switch ($result['ActionCode']) {
                // visa mastercard
                case '005':
                    $xmlArray['ErrMsg'] = "Card has been Declined. Please try a different card or reenter your information!";
                    break;
                case '051':
                    $xmlArray['ErrMsg'] = "Insufficient funds. Please try a different card.";
                    break;
                case '054':
                    $xmlArray['ErrMsg'] = "Expired Card. Please try a different card.";
                    break;
                case '806';
                    $xmlArray['ErrMsg'] = "CVV code Failure. Please try a different card or reenter your information.";
                    break;
                // American Express
                case '100':
                    $xmlArray['ErrMsg'] = "Card has been Declined. Please try a different card or reenter your information.";
                    break;
                case '110':
                    $xmlArray['ErrMsg'] = "Invalid amount. Please try a different card.";
                    break;
                case '101':
                    $xmlArray['ErrMsg'] = "Expired Card. Please try a different card.";
                    break;
                case '940':
                    $xmlArray['ErrMsg'] = "Charge already settled. Please issue a credit instead.";
                    $credit = true;
                    break;
                default:
                    $xmlArray['ErrMsg'] = "Card has been Declined! Please try a different card or reenter your information.";
            }
            // echo $result['ActionCode'];
            // credit card did not go through.------------------------------ 
            // create error and go back to credit card page
            $flash = $this->get('braincrafted_bootstrap.flash');
            $flash->error('Credit Card Error: ' . $xmlArray['ErrMsg']);
            // fill data array
            $data = array(
                "cardnumber" => $request->request->get('ccnumber'),
                "expmonth" => $request->request->get('month'),
                "expyear" => $request->request->get('year'),
                "approval" => $request->request->get('approval'),
                "customer" => $request->request->get('customer'),
                "amount" => $request->request->get('totalamount'),
                "transactionid" => $transid
            );
            if ($credit)
                return $this->render('HSFrontendBundle:Admin:credit.html.twig', array('data' => $data, 'sandbox' => $sandbox));
            return $this->render('HSFrontendBundle:Admin:void.html.twig', array('data' => $data, 'sandbox' => $sandbox));
        }
    }

    public function processcreditAction($transid, Request $request) {
        $em = $this->getDoctrine()->getManager();

        $totalAmount = str_replace('.', '', $request->request->get('totalamount'));
        // remove the negative
        $totalAmount = str_replace('-', '', $totalAmount);
        $action = $request->request->get('action');

        $card = $this->get('creditcard');

        $card->setCardNum($request->request->get('ccnumber'));
        $card->setCardExpMonth($request->request->get('month'));
        $card->setCardExpYear($request->request->get('year'));
        $card->setTotalAmount($totalAmount);
        $card->setApproval($request->request->get('approval'));

        if ($action == 'void') {
            $result = $card->voidRequest($transid, $sandbox);
        } else {
            $result = $card->creditRequest($transid, $sandbox);
        }

        if ($result['ActionCode'] == '000') {
            // success credit has been applied
            // store transaction and display result
            $order = new Orders();

            $order->setTransactionID($result['TransactionID']);
            $order->setApproval($result['Approval']);
            $order->setPackage('na');
            $order->setPrice('-' . $request->request->get('totalamount'));
            $order->setCustomer('engtwrs');
            $order->setCcnum(substr($card->getCardNum(), -4));
            $order->setCallingStationId('na');
            $order->setPhoneNumber('na');
            $order->setNote($request->request->get('note'));
            $order->setCreatedAt(new \DateTime('now'));

            $em->persist($order);
            $em->flush();
            // set notification, clear card data and render order page.
            $flash = $this->get('braincrafted_bootstrap.flash');
            $flash->alert($result['ResponseText']);
            $route = $this->generateUrl('hs_frontend_transactions');
            return $this->redirect($route);            
        }
        // more things gone wrong            
        $credit = false;
        switch ($result['ActionCode']) {
            // visa mastercard
            case '005':
                $xmlArray['ErrMsg'] = "Card has been Declined. Please try a different card or reenter your information!";
                break;
            case '051':
                $xmlArray['ErrMsg'] = "Insufficient funds. Please try a different card.";
                break;
            case '054':
                $xmlArray['ErrMsg'] = "Expired Card. Please try a different card.";
                break;
            case '806';
                $xmlArray['ErrMsg'] = "CVV code Failure. Please try a different card or reenter your information.";
                break;
            // American Express
            case '100':
                $xmlArray['ErrMsg'] = "Card has been Declined. Please try a different card or reenter your information.";
                break;
            case '110':
                $xmlArray['ErrMsg'] = "Invalid amount. Please try a different card.";
                break;
            case '101':
                $xmlArray['ErrMsg'] = "Expired Card. Please try a different card.";
                break;
            case '940':
                $xmlArray['ErrMsg'] = "Charge already settled. Please issue a credit instead.";
                $credit = true;
                break;
            default:
                $xmlArray['ErrMsg'] = "Card has been Declined! Please try a different card or reenter your information.";
        }
        // echo $result['ActionCode'];
        // credit card did not go through.------------------------------ 
        // create error and go back to credit card page
        $flash = $this->get('braincrafted_bootstrap.flash');
        $flash->error('Credit Card Error: ' . $xmlArray['ErrMsg']);
        // fill data array
        $data = array(
            "cardnumber" => $request->request->get('ccnumber'),
            "expmonth" => $request->request->get('month'),
            "expyear" => $request->request->get('year'),
            "approval" => $request->request->get('approval'),
            "amount" => $request->request->get('totalamount'),
            "transactionid" => $transid
        );
        if ($credit)
            return $this->render('HSFrontendBundle:Admin:credit.html.twig', array('data' => $data, 'sandbox' => $sandbox));
        return $this->render('HSFrontendBundle:Admin:void.html.twig', array('data' => $data, 'sandbox' => $sandbox));
    }

    public function transactionidAction() {

        $em = $this->getDoctrine()->getManager();
        $query = $em->createQuery(
                "SELECT o
             FROM HSFrontendBundle:Orders o
             ORDER BY o.createdAt DESC"
        );
        $orders = $query->getResult();

        return $this->render('HSFrontendBundle:Admin:transactionid.html.twig');
    }

    public function orderAction(Request $request) {
        $sandbox = $this->getSandbox();
        $carddata = array(
            'cardnumber' => '',
            'cvv' => '',
            'zip' => '',
            'month' => '',
            'year' => '',
            'totalamount' => '',
            'phonenumber' => '');
        return $this->render('HSFrontendBundle:Admin:order.html.twig', array('carddata' => $carddata, 'sandbox' => $sandbox));
    }

    public function activeAction(Request $request) {

        // English Towers

        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery(
                "SELECT s.server
             FROM HSFrontendBundle:Nas s
             WHERE s.community = 'engtwrs'");

        $etClients = $query->getResult();

        $api = $this->get('routeros');
        $login = "admin";
        $password = "3SHOREn3t";

        $api->debug = false;
        $ETResults = array();
        $counter1 = 0;
        foreach ($etClients as $etclient) {
            if ($api->connect($etclient['server'], $login, $password)) {
                $api->write('/interface/wireless/registration-table/print');

                $read = $api->read(false);
                $results = $api->parseResponse($read);
                $counter2 = 0;
                foreach ($results as $result) {
                    foreach ($result as $key => $value) {
                        $key = str_replace('-', '', $key);
                        if ($key == 'interface')
                            $key = 'user';
                        if ($key == 'bytes') {
                            $value = explode(',', $value);
                            $key = "bytesout";
                            $value1 = round($value[0] / 1000000, 2);
                            $ETResults[$counter1][$counter2][$key] = $value1;
                            $key = "bytesin";
                            $value2 = round($value[1] / 1000000, 2);
                            $ETResults[$counter1][$counter2][$key] = $value2;
                        } else {
                            $ETResults[$counter1][$counter2][$key] = $value;
                        }
                    }
                    $counter2++;
                }
                $counter1++;
                $api->disconnect();
            }
        }

        $api = $this->get('routeros');
        $login = "admin";
        $password = "";
        $ip = "**************";

        $api->debug = false;
        $newResults = array();

        if ($api->connect($ip, $login, $password)) {
            $api->write('/ip/hotspot/active/print');

            $read = $api->read(false);
            $results = $api->parseResponse($read);
            $counter = 0;
            foreach ($results as $result) {
                foreach ($result as $key => $value) {
                    $key = str_replace('-', '', $key);
                    if ($key == 'bytesin' || $key == 'bytesout')
                        $value = round($value / 1000000, 2);
                    $newResults[$counter][$key] = $value;
                }
                $counter++;
            }

            $api->disconnect();
        }



        $query = $em->createQuery(
                'SELECT s
                 FROM HSFrontendBundle:Radacct s
                 WHERE s.acctstoptime IS NULL');

        $activeSessions = $query->getResult();

        if (!$activeSessions) {
            $activeSessions = array();
        }

        return $this->render('HSFrontendBundle:Admin:active.html.twig', array('activeSessions' => $activeSessions, 'results' => $newResults, 'ETResults' => $ETResults));
    }

    /*
     * Activenetworks
     * Query database and retrieve every order that has not expired
     * 
     */

    public function activenetworksAction(Request $request) {

        $em = $this->getDoctrine()->getManager();

        $active = $this->getActiveNeworks();

        $query = $em->createQuery(
                'SELECT s
                 FROM HSFrontendBundle:Radreply s
                ');
        try {
            //$results = $query->getArrayResult();
            $results = $query->getResult();
            //print_r($results);die();
        } catch (\Doctrine\ORM\NoResultException $e) {
            $results = array();
        }

        $query2 = $em->createQuery(
                "SELECT s
                 FROM HSFrontendBundle:Radreply s
                 WHERE s.attribute = 'Mikrotik-Rate-Limit' 
                ");
        try {
            //$results = $query->getArrayResult();
            $speeds = $query2->getArrayResult();
            //print_r($results);die();
        } catch (\Doctrine\ORM\NoResultException $e) {
            $results = array();
        }
        foreach ($results as $result) {
            if ($result->getAttribute() == 'WISPr-Session-Terminate-Time') {
                $expires = str_replace('T', ' ', $result->getValue());
                $timeleft = (strtotime($expires) - time());
                if ($timeleft > 0) {
                    $username = $result->getUserName();
                    $account = $em->getRepository('HSFrontendBundle:Radcheck')->findOneByUsername($username);
                    $speed = 0;
                    foreach ($speeds as $speed) {
                        if ($speed['username'] == $username) {

                            $current_speed = $speed['value'];
                        }
                    }
                    $active[] = array(
                        'unit' => $username,
                        'password' => $account->getValue(),
                        'speed' => $current_speed,
                        'days' => '0',
                        'expires' => date('D M jS \a\t H:i:s', strtotime($expires))
                    );
                }
            }
        }

        return $this->render('HSFrontendBundle:Admin:activenetworks.html.twig', array('active' => $active));
    }

    public function statusinfoAction($password) {
        $results = $this->getDoctrine()->getRepository('HSFrontendBundle:Nas')->findONeBy(
                array('secret' => $password));
        return $this->render('HSFrontendBundle:Admin:statusinfo.html.twig', array('nas' => $results));
    }

    public function statusAction(Request $request) {

        if (null !== filter_input(INPUT_POST, 'LOOKUP')) {
            //if ( isset( filter_input(INPUT_POST, 'LOOKUP'))) { 


            $password = filter_input(INPUT_POST, 'password');
            //$password = $request->request->get('password'); 
            //    return $this->render('HSFrontendBundle:Admin:statusinfo.html.twig', array('nas' => $results));  
        } else {
            $password = $request->request->get('password');
            $min = 10;
            //return $this->render('HSFrontendBundle:Admin:status.html.twig', array('password' => $password, 'min' => $min));             
        }
        //if (isset($password)) {
        //$results = $this->getDoctrine()->getRepository('HSFrontendBundle:Nas')->findOneBy(array('secret' => $password));
        //    if (!$results) {
        //        $flash = $this->get('braincrafted_bootstrap.flash');
        //        $flash->success('No order found, did you enter the correct password?');
        //        $success = false;
        //    } else {
        //        $success = true;
        //    }
        //}
        $min = 30;
        if ($password == NULL) {
            $password = 'stillNULL';
        }
        return $this->render('HSFrontendBundle:Admin:status.html.twig', array('password' => $password, 'min' => $min));
    }

    private function getSandbox() {
        return false;
        $sandbox = $this->container->getParameter('sandbox');
        $usr = $this->get('security.token_storage')->getToken()->getUser();
        $usr->getUsername();
        if ($usr == 'webdev') {
            $sandbox = true;
        }
        return $sandbox;
    }

    private function getInfo($password) {

        $em = $this->getDoctrine()->getManager();

        $query = $em->createQuery(
                "SELECT n.nas
             FROM HSFrontendBundle:Nas n
             WHERE n.secret = '" . $password . "'");

        $info = $query->getResult();

        return $info;
    }
    
    private function isAlive($ip) {
        /* PING TEST */
        $api = $this->get('routeros');
        $api->debug = false;
        return $api->isAlive($ip);
    }
    
    private function getSystemId($ip) {
        $api = $this->get('routeros');
        $api->debug = false;
        $login = "admin";
        $password = "3SHOREn3t";
        return $api->getSystemId($ip, $login, $password);
    }

}
