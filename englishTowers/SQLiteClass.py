import sqlite3
from datetime import datetime, timedelta, date
from calendar import monthrange




class SQLiteClass:
    def connect_sqlite(self):
        """Establish SQLite connection."""
        connection = sqlite3.connect("sf_hotspot.sqlite")
        connection.row_factory = sqlite3.Row  # Enables dictionary-like row access
        return connection

    def select_query(self, query, *params):
        """Executes a SELECT query and returns results as dictionaries."""
        with self.connect_sqlite() as connection:
            cursor = connection.execute(query, params)
            return [dict(row) for row in cursor.fetchall()]

    def save_query(self, query, *params, returnNewIDInsteadOfRowCount=False):
        with self.connect_sqlite() as connection:
            print(f"query: {query}\nparams: {params}\n")
            # Fix: Unpack the tuple if it's a single-element tuple containing another tuple
            if len(params) == 1 and isinstance(params[0], tuple):
                params = params[0]
            cursor = connection.execute(query, params)
            connection.commit()
            return cursor.lastrowid if returnNewIDInsteadOfRowCount else cursor.rowcount

    def get_router(self, ipaddress):
        return self.select_query("SELECT hostname FROM router WHERE ipaddress = ?", (ipaddress,))

    def router_turn_rental_off(self, ipaddress):
        if ipaddress is None:
            return 0
        # Ensure ipaddress is a string
        ipaddress = str(ipaddress)
        return self.save_query("UPDATE router SET rental = 0 WHERE ipaddress = ?", ipaddress)

    def update_nas(self, hostname, ipaddress):
        return self.save_query("UPDATE nas SET ipaddress = ? WHERE shortname = ?", (ipaddress, hostname))

    def save_router(self, params):
        """Insert or update router information."""
        existing = self.select_query("SELECT * FROM router WHERE hostname = ?", (params['hostname'],))
        if existing:
            query = """
                UPDATE router 
                SET model = ?, firmware = ?, rental = ?, ssid_24 = ?, freq_24 = ?, 
                    ssid_5 = ?, freq_5 = ?, ssid_pwd = ?, ssid_clients = ?, queue_name = ?, ipaddress = ?
                WHERE hostname = ?
            """
            values = (
                params['model'], params['firmware'], params['rental'], params['ssid_24'],
                params['freq_24'], params['ssid_5'], params['freq_5'], params['ssid_pwd'],
                params['ssid_clients'], params['queue_name'], params['ipaddress'], params['hostname']
            )
        else:
            query = """
                INSERT INTO router (hostname, model, firmware, rental, ssid_24, freq_24, ssid_5, freq_5, 
                                   ssid_pwd, ssid_clients, queue_name, ipaddress)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            values = (
                params['hostname'], params['model'], params['firmware'], params['rental'], params['ssid_24'],
                params['freq_24'], params['ssid_5'], params['freq_5'], params['ssid_pwd'], params['ssid_clients'],
                params['queue_name'], params['ipaddress']
            )
        return self.save_query(query, *values)

    def get_routers(self):
        return self.select_query("SELECT hostname, model, firmware, rental, queue_name, ipaddress FROM router")

    def get_unit_orders(self, id):
        return self.select_query("SELECT price, transactionID, approval, created_at FROM orders WHERE unit = ?",  id)

    def get_unit_router_ip(self, id):
        unit_id = 'et' + id
        result = self.select_query("SELECT ipaddress FROM nas WHERE shortname = ?", unit_id)
        return result[0]['ipaddress'] if result else None

    def finalize_new_order(self, id):
        unit_id = 'et' + id
        result = self.select_query("SELECT ipaddress FROM nas WHERE shortname = ?", (unit_id,))
        return result[0]['ipaddress'] if result else None

    def get_unit_id(self, id):
        unit_id = 'et' + id
        result = self.select_query("SELECT id FROM nas WHERE shortname = ?",  unit_id, )
        return result[0]['id'] if result else None

    def new_network(self, unit, password, days, package, email, price,hard_expiration_date):
        print(f"new_network: {unit} {password} {days} {package} {email}" )

        unit_id = self.get_unit_id(unit)
        if unit_id is None:
            return None, "Could not find unit. Please check your spelling and try again."
        print ("unit_id: ", unit_id)
        speed = 50 if package == 'streaming' else 25
        now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        sql_statement = f"INSERT INTO orders (et_nasid, et_passwd, speed, et_days, created_at,unit, email, package, price, hard_expiration_date) VALUES ( {unit_id} ,'{password}', {speed}, {days},  '{now}', '{unit}', '{email}','{package}',{price},'{hard_expiration_date}')"
        print (f"sql_statement: {sql_statement}")
        return self.save_query(sql_statement, returnNewIDInsteadOfRowCount=True ), "Success."

    def get_active_networks(self):
        results = self.select_query(
            """
         SELECT orders.et_passwd as passwd, orders.speed as speed, orders.et_days as days, 
                orders.created_at as created_at, nas.shortname as shortname, 
                orders.hard_expiration_date as hard_expiration_date
         FROM orders
         INNER JOIN nas ON orders.et_nasid = nas.id
            """
        )

        response = []
        now = date.today()
        for result in results:
            created_at = datetime.strptime(result['created_at'], '%Y-%m-%d %H:%M:%S').date()
            
            # Calculate days left based on hard_expiration_date if it exists
            if result['hard_expiration_date'] and result['hard_expiration_date'] != 'None':
                hard_expiration = datetime.strptime(result['hard_expiration_date'], '%Y-%m-%d').date()
                days_left = (hard_expiration - now).days
                # If hard expiration date has passed, set days_left to 0
                if days_left <= 0:
                    days_left = 0
            else:
                # Original calculation if no hard expiration date
                target = created_at + timedelta(days=int(result['days']))
                days_left = (target - now).days

            if days_left >= 0:
                response.append({
                    'passwd': result['passwd'],
                    'speed': result['speed'],
                    'days': result['days'],
                    'daysLeft': days_left,
                    'unit': result['shortname'],
                    'shortname': f'<a href="unit/{result["shortname"][2:]}">{result["shortname"][2:]}</a>'
                })
        return response

    def get_recent_order_info(self):
        """Retrieve recent (past 2 days) general order info."""
        today = date.today()
        two_days_ago = today - timedelta(days=2)

        order_info = self.select_query(f"""
            SELECT email, price, package, unit, et_passwd, speed, et_days, isPaid
            FROM orders  
            WHERE date(created_at) BETWEEN date('{two_days_ago.strftime("%Y-%m-%d")}') 
            AND date('{today.strftime("%Y-%m-%d")}') 
            ORDER BY created_at ASC
        """)

        customer_info_list = []

        if order_info:
            for order in order_info:
                summary = (f"pwd: {order['et_passwd']} "
                           f"pkg: {order['et_days']} of {order['package']} x{order['speed']} "
                           f"for unit {order['unit']}")

                status = "Confirmed" if order['isPaid'] == 1 else "Pending"

                order_data = {
                    "email": order["email"],
                    "status": status,
                    "order_summary": summary
                }

                customer_info_list.append(order_data)

        return customer_info_list if customer_info_list else None

    def get_orders_mtd(self):
        """Retrieve month-to-date orders."""
        today = date.today()
        return self.select_query(
            f"""
            SELECT price, unit, transactionID, approval, created_at 
            FROM orders  
            WHERE date(created_at) BETWEEN date('{today.replace(day=1).strftime("%Y-%m-%d")}') 
            AND date('{today.replace(day=monthrange(today.year, today.month)[1]).strftime("%Y-%m-%d")}') 
            ORDER BY created_at ASC
            """
        )

    def update_order(self,transaction_id,order_id, receipt_url ):
        """Finalize order with transactional information and receipt url."""
        today = date.today()

        sql_statement = f"""
                UPDATE orders
                SET    transactionID = '{transaction_id}', 
                       receipt_url = '{receipt_url}', 
                       isPaid = 1
                WHERE id = {order_id};
                       """
        row_count = self.save_query(sql_statement)

        if row_count < 1:
            return None

        email = None
        select_statement = "SELECT email, et_passwd FROM orders WHERE id = ?"
        result = self.select_query(select_statement, order_id,)

        if result:
            email = result[0].get('email', None)
            et_passwd = result[0].get('et_passwd', None)
            return email, et_passwd

        return

    def get_customer_email_and_key(self, order_id):
        email = None
        select_statement = "SELECT email, et_passwd FROM orders WHERE id = ?"
        result = self.select_query(select_statement, order_id,)

        if result:
            email = result[0].get('email', None)
            et_passwd = result[0].get('et_passwd', None)
            return email, et_passwd

        return None


