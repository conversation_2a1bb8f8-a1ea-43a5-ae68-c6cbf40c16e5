import routeros_api
import SQLiteClass
from turtle import *
#
# Mikrotik functions
# https://pypi.org/project/RouterOS-api/
#
def router_connect(ip):
    connection = routeros_api.RouterOsApiPool(
        ip,
        username='admin',
        password='3SHOREn3t',
        plaintext_login=True,
        use_ssl=False
    )
    api = connection.get_api()
    return api

# /queue/simple
# {'id': '*1', 'name': 'Owner 50Mb 050121', 'target': '************/24', 'dst': 'ether1',
#  'parent': 'none', 'packet-marks': '', 'priority': '8/8', 'queue': 'ethernet-default/ethernet-default',
#  'limit-at': '0/0', 'max-limit': '50000000/50000000', 'burst-limit': '55000000/55000000',
#  'burst-threshold': '50000000/50000000', 'burst-time': '5s/5s', 'bucket-size': '0.1/0.1',
#  'bytes': '39524225234/1644368280460', 'total-bytes': '0', 'packets': '421481538/1142673942',
#  'total-packets': '0', 'dropped': '28204/124325613', 'total-dropped': '0', 'rate': '0/0',
#  'total-rate': '0', 'packet-rate': '0/0', 'total-packet-rate': '0', 'queued-packets': '0/0',
#  'total-queued-packets': '0', 'queued-bytes': '0/0', 'total-queued-bytes': '0', 'invalid': 'false',
#  'dynamic': 'false', 'disabled': 'false'}

# /interface/wireless
# {'id': '*B', 'name': 'Rental', 'mtu': '1500', 'l2mtu': '1600', 'mac-address': 'CE:2D:E0:C4:D5:B3',
# 'arp': 'enabled', 'arp-timeout': 'auto', 'disable-running-check': 'false', 'interface-type': 'virtual',
# 'master-interface': 'wlan1', 'mode': 'ap-bridge', 'ssid': 'Rental602', 'area': '',
# 'max-station-count': '2007', 'vlan-mode': 'no-tag', 'vlan-id': '1', 'wds-mode': 'disabled',
# 'wds-default-bridge': 'none', 'wds-default-cost': '100', 'wds-cost-range': '50-150',
# 'wds-ignore-ssid': 'false', 'update-stats-interval': 'disabled', 'bridge-mode': 'enabled',
# 'default-authentication': 'true', 'default-forwarding': 'true', 'default-ap-tx-limit': '0',
# 'default-client-tx-limit': '0', 'wmm-support': 'disabled', 'hide-ssid': 'false',
# 'security-profile': 'Rental', 'interworking-profile': 'disabled', 'wps-mode': 'push-button',
# 'station-roaming': 'enabled', 'station-bridge-clone-mac': '00:00:00:00:00:00',
# 'multicast-helper': 'default', 'multicast-buffering': 'enabled', 'keepalive-frames': 'enabled',
# 'running': 'false', 'disabled': 'false'}

def router_get_resource(api, resource):
    list_queues = api.get_resource(resource)
    return list_queues.get()

#
# Application
#
database = SQLiteClass()
result = database.select_query("SELECT `shortname`,`ipaddress` FROM `nas` WHERE `community`=%s", "engtwrs")
for i in result:
    print(i['shortname'] + ' : ' + i['ipaddress'])
    api = router_connect(i['ipaddress'])
    # resource = '/queue/simple'
    # resource = '/interface/wireless'
    # resource = '/interface/wireless/security-profiles'
    # resource = '/ip/addres'
    # resource = '/interface/bridge'
    # resource = '/system/identity'
    resource = '/interface/bridge/port'
    queues = router_get_resource(api, resource)
    for queue in queues:
        print(queue['interface'])
    break