import routeros_api
from routeros_api.exceptions import RouterOsApiCommunicationError


#
# Mikrotik functions
# https://pypi.org/project/RouterOS-api/
# https://github.com/socialwifi/RouterOS-api
#
class mtikClass:
    def router_connect(self, ip):
        if ip is None:
            return None, "No IP address"
        try:
            connection = routeros_api.RouterOsApiPool(
                ip,
                username='admin',
                password='3SHOREn3t',
                plaintext_login=True,
                use_ssl=False
            )
            api = connection.get_api()
        except RouterOsApiCommunicationError as e:
            return None, f"RouterOS API Communication Error: {str(e)}"
        except Exception as e:
            return None, f"Connection Error: {str(e)}"

        return api, "Success"

    def router_get_resource(self, ipaddress,resource):
        api = None
        api_result = self.router_connect(ipaddress)
        if not api_result[0]:
            return api_result[1]
        api = api_result[0]
        list_resources = api.get_resource(resource)
        return list_resources.get()

    def get_router_hostname(self,ipaddress):
        api=None
        api_result = self.router_connect(ipaddress)
        if not api_result[0]:
            return api_result[1]
        api=api_result[0]
        list_resource = api.get_resource('/system/identity')
        list_identity = list_resource.get()
        return list_identity[0]['name']

    def get_router_info(self, ipaddress):
        api = None
        api_result = self.router_connect(ipaddress)
        if not api_result[0]:
            return api_result[1]
        api = api_result[0]
        list_resource = api.get_resource('/system/identity')
        list_identity = list_resource.get()
        hostname = list_identity[0]['name']
        list_resource = api.get_resource('/system/routerboard')
        list_routerboard = list_resource.get()
        model = list_routerboard[0]['model']
        current_firmware = list_routerboard[0]['current-firmware']
        list_resource = api.get_resource('/interface/wireless')
        list_wireless_interfaces = list_resource.get()
        has_rental = False
        ssid_5 = ''
        freq_5 = 0
        for wireless_interface in list_wireless_interfaces:
            if(wireless_interface['ssid'][:6] == "Rental"):
                has_rental = True
            else:
                if(wireless_interface['band'][:4] == "2ghz"):
                    ssid_24 = wireless_interface['ssid']
                    freq_24 = wireless_interface['frequency']
                elif(wireless_interface['band'][:4] == "5ghz"):
                    ssid_5 = wireless_interface['ssid']
                    freq_5 = wireless_interface['frequency']
        list_resource = api.get_resource('/interface/wireless/security-profiles')
        list_security_profile = list_resource.get(name='default')
        wireless_password = list_security_profile[0]['wpa-pre-shared-key']
        list_resource = api.get_resource('/interface/wireless/registration-table')
        wireless_clients = len(list_resource.get())
        list_resource = api.get_resource('/queue/simple')
        list_queue = list_resource.get()
        queue_name = list_queue[0]['name']
        result = ({
            'hostname':hostname,'model':model,'firmware':current_firmware,'rental':has_rental,
            'ssid_24':ssid_24,'freq_24':freq_24,'freq_5':freq_5,'ssid_5':ssid_5,'ssid_pwd':wireless_password,'ssid_clients':wireless_clients,
            'queue_name':queue_name, 'ipaddress':ipaddress
        })
        return result

    def get_ssid(self,ipaddress):
        ssids = self.router_get_resource(ipaddress,'/interface/wireless')
        pwds = self.router_get_resource(ipaddress,'/interface/wireless/security-profiles')
        result = []
        for ssid in ssids:
            for pwd in pwds:
                if ssid['security-profile'] == pwd['name']:
                    password = pwd['wpa2-pre-shared-key']
                    break
            if 'master-interface' in ssid:
                result.append({
                    'ssid': ssid['ssid'],
                    'password': password,
                    'master-interface': ssid['master-interface'],
                    'security-profile': ssid['security-profile'],
                    'mac-address': ssid['mac-address'],
                    'band': 'na',
                    'channel-width': 'na',
                    'frequency': 'na',
                })
            else:
                result.append({
                    'ssid': ssid['ssid'],
                    'password': password,
                    'master-interface': ssid['name'],
                    'security-profile': ssid['security-profile'],
                    'mac-address': ssid['mac-address'],
                    'band': ssid['band'],
                    'channel-width': ssid['channel-width'],
                    'frequency': ssid['frequency'],
                })
        return result

    def get_registration_list(self,ipaddress):
        registrations = self.router_get_resource(ipaddress,'/interface/wireless/registration-table')
        return registrations

    def get_queue(self,ipaddress):
        queues = self.router_get_resource(ipaddress,'/queue/simple')
        return queues

    def add_queue(self,ipaddress,queue):
        # list_queues.add(name="001", max_limit="512k/4M", target="************/32")
        api = None
        api_result = self.router_connect(ipaddress)
        if not api_result[0]:
            return api_result[1]
        api = api_result[0]
        list_queues = api.get_resource('/queue/simple')
        # delete existing queues
        old_queues = list_queues.get()
        for old_queue in old_queues:
            list_queues.remove(id=old_queue['id'])
        list_queues.add(name=queue['name'],max_limit=queue['max_limit'],target=queue['target'],queue=queue['queue'],
                        burst_limit=queue['burst_limit'], burst_threshold=queue['burst_threshold'],dst=queue['dst'],
                        burst_time=queue['burst_time'])
        return True

    def remove_interface(self,resource,interface):
        found = resource.get(interface=interface)
        if not found:
            return interface + " Not Found"
        else:
            resource.remove(id=found[0]['id'])
        return interface + " Removed"

    def remove_resource(self,resource,name):
        found = resource.get(name=name)
        if not found:
            return name + " Not Found"
        else:
            resource.remove(id=found[0]['id'])
        return name + " Removed"

    def reset_ap(self,ipaddress):
        api = None
        api_result = self.router_connect(ipaddress)
        if not api_result[0]:
            return api_result[1]
        api = api_result[0]
        result = []
        # 1 Remove Rental security profile
        resource = api.get_resource('/interface/wireless/security-profiles')
        result.append('Security Profile: ' + self.remove_resource(resource,'Rental'))
        resource = api.get_resource('/interface/bridge/port')
        result.append('Bridge: ' + self.remove_interface(resource,'Rental'))
        result.append('Bridge: ' + self.remove_interface(resource, 'Rental5G'))
        resource = api.get_resource('/interface/wireless')
        result.append('Wireless: ' + self.remove_resource(resource,'Rental'))
        result.append('Wireless: ' + self.remove_resource(resource, 'Rental5G'))
        resource = api.get_resource('/queue/simple')
        result.append('Queue: ' + self.remove_resource(resource,'Rental'))
        return result

    def new_rental(self,ipaddress,unit,password,package):
        print("mtikClass.new_rental")
        api = None
        api_result = self.router_connect(ipaddress)
        print(f"api_result", api_result )
        if not api_result[0]:
            return api_result[1]
        api = api_result[0]
        print("before get resource")
        #1 add security profile
        list_secpro = api.get_resource('/interface/wireless/security-profiles')
        security_profiles = list_secpro.get()
        print("before secpro.add")
        try:
            list_secpro.add(name='Rental', wpa_pre_shared_key=password, wpa2_pre_shared_key=password,
                            mode='dynamic-keys', authentication_types='wpa-psk,wpa2-psk')
        except RouterOsApiCommunicationError as e:
            if "profile with the same name already exists" in str(e):
              return False, "Security profile 'Rental' already exists. Skipping creation."
          # Re-raise other unexpected errors
        list_wifi = api.get_resource('/interface/wireless')
        ssids = list_wifi.get()
        list_bridge_ports = api.get_resource('/interface/bridge/port')
        for ssid in ssids:
            if(ssid['name'] == 'wlan1'):
                list_wifi.add(name='Rental',ssid='Rental'+unit,security_profile='Rental',master_interface='wlan1',disabled='no')
                list_bridge_ports.add(interface='Rental',bridge='bridge')
            elif(ssid['name'] == 'wlan2'):
                list_wifi.add(name='Rental5G', ssid='Rental'+unit, security_profile='Rental',master_interface='wlan2',disabled='no')
                list_bridge_ports.add(interface='Rental5G', bridge='bridge')
        #3 add queue based on packages. Basic 25/15 Streaming 50/15
        list_queue = api.get_resource('/queue/simple')
        queues = list_queue.get()
        if package == 'standard':
            list_queue.add(name='Rental',place_before='0',target='************/24',dst='ether1',max_limit='15M/25M',burst_threshold='15M/25M',burst_limit='20M/30M',burst_time='5s/5s')
        elif package == 'streaming':
            list_queue.add(name='Rental', place_before='0', target='************/24',dst='ether1', max_limit='15M/50M',burst_threshold='15M/50M', burst_limit='20M/60M', burst_time='5s/5s')
        return True,"Success."
        # br_ports = api.get_resource(ipaddress,'/interface/bridge/port')
        # queues = api.get_resource(ipaddress,'/queue/simple')
        # ssids = api.get_resource(ipaddress,'/interface/wireless')

    # /queue/simple
    # {'id': '*1', 'name': 'Owner 50Mb 050121', 'target': '************/24', 'dst': 'ether1',
    #  'parent': 'none', 'packet-marks': '', 'priority': '8/8', 'queue': 'ethernet-default/ethernet-default',
    #  'limit-at': '0/0', 'max-limit': '50000000/50000000', 'burst-limit': '55000000/55000000',
    #  'burst-threshold': '50000000/50000000', 'burst-time': '5s/5s', 'bucket-size': '0.1/0.1',
    #  'bytes': '39524225234/1644368280460', 'total-bytes': '0', 'packets': '421481538/1142673942',
    #  'total-packets': '0', 'dropped': '28204/124325613', 'total-dropped': '0', 'rate': '0/0',
    #  'total-rate': '0', 'packet-rate': '0/0', 'total-packet-rate': '0', 'queued-packets': '0/0',
    #  'total-queued-packets': '0', 'queued-bytes': '0/0', 'total-queued-bytes': '0', 'invalid': 'false',
    #  'dynamic': 'false', 'disabled': 'false'}

    # /interface/wireless
    # {'id': '*B', 'name': 'Rental', 'mtu': '1500', 'l2mtu': '1600', 'mac-address': 'CE:2D:E0:C4:D5:B3',
    # 'arp': 'enabled', 'arp-timeout': 'auto', 'disable-running-check': 'false', 'interface-type': 'virtual',
    # 'master-interface': 'wlan1', 'mode': 'ap-bridge', 'ssid': 'Rental602', 'area': '',
    # 'max-station-count': '2007', 'vlan-mode': 'no-tag', 'vlan-id': '1', 'wds-mode': 'disabled',
    # 'wds-default-bridge': 'none', 'wds-default-cost': '100', 'wds-cost-range': '50-150',
    # 'wds-ignore-ssid': 'false', 'update-stats-interval': 'disabled', 'bridge-mode': 'enabled',
    # 'default-authentication': 'true', 'default-forwarding': 'true', 'default-ap-tx-limit': '0',
    # 'default-client-tx-limit': '0', 'wmm-support': 'disabled', 'hide-ssid': 'false',
    # 'security-profile': 'Rental', 'interworking-profile': 'disabled', 'wps-mode': 'push-button',
    # 'station-roaming': 'enabled', 'station-bridge-clone-mac': '00:00:00:00:00:00',
    # 'multicast-helper': 'default', 'multicast-buffering': 'enabled', 'keepalive-frames': 'enabled',
    # 'running': 'false', 'disabled': 'false'}

    def security_profile_exists(self, ipaddress, profile_name):
        """
        Check if a security profile exists on the router
        
        Args:
            ipaddress: Router IP address
            profile_name: Name of the security profile to check
        
        Returns:
            bool: True if profile exists, False otherwise
        """
        api_result = self.router_connect(ipaddress)
        if not api_result[0]:
            return False
        
        api = api_result[0]
        list_secpro = api.get_resource('/interface/wireless/security-profiles')
        security_profiles = list_secpro.get(name=profile_name)
        
        return len(security_profiles) > 0
