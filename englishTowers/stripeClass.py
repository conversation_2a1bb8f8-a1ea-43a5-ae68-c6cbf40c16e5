import stripe
from django.conf import settings

class myStripe:

    def __init__(self, strStr):
        # Make sure to use the secret key, not a publishable key
        stripe.api_key = settings.STRIPE_SECRET_KEY
        acct_id = settings.STRIPE_ACCOUNT_ID
        stripe.Account(acct_id)

    def payment(self):
        try:
            # Use <PERSON><PERSON>'s library to make requests...
            pass
        except stripe.error.CardError as e:
            # Since it's a decline, stripe.error.CardError will be caught

            print('Status is: %s' % e.http_status)
            print('Code is: %s' % e.code)
            # param is '' in this case
            print('Param is: %s' % e.param)
            print('Message is: %s' % e.user_message)
        except stripe.error.RateLimitError as e:
            # Too many requests made to the API too quickly
            pass
        except stripe.error.InvalidRequestError as e:
            # Invalid parameters were supplied to Stripe's API
            pass
        except stripe.error.AuthenticationError as e:
            # Authentication with <PERSON>e's API failed
            # (maybe you changed API keys recently)
            pass
        except stripe.error.APIConnectionError as e:
            # Network communication with <PERSON><PERSON> failed
            pass
        except stripe.error.StripeError as e:
            # Display a very generic error to the user, and maybe send
            # yourself an email
            pass
        except Exception as e:
            # Something else happened, completely unrelated to Stripe
            pass
