import datetime as dt
from scheduler import Scheduler
from scheduler.trigger import Monday, Tuesday
from NC811.nc811Class import NC811
from englishTowers.SQLiteClass import MysqlClass
from englishTowers.mtikClass import mtikClass
import nmap

def nc811_refresh_tickets():
    nc811_api = NC811()
    nc811_api.refresh_tickets()

def et_update_routers():
    nm = nmap.PortScanner()
    nm.scan(hosts='*********/23', arguments='-sn')
    hosts_list = [(x, nm[x]['status']['state']) for x in nm.all_hosts()]
    for host, status in hosts_list:
        mtik = mtikClass()
        hostname = mtik.get_router_hostname(host)
        count = 0
        if hostname != 'Connection Error':
            count += 1
            db = MysqlClass()
            db.update_nas('et' + hostname, host)

schedule = Scheduler()
schedule.hourly(dt.time(minute=30),nc811_refresh_tickets)
schedule.weekly(Monday(),et_update_routers)
