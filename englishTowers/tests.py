from django.test import TestCase
from django.urls import reverse
from django.contrib.auth.models import User

class EnglishTowersTests(TestCase):
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword'
        )
    
    def test_main_view(self):
        """Test that the main page loads correctly"""
        self.client.login(username='testuser', password='testpassword')
        # Use the correct URL name - adjust this to match your actual URL configuration
        response = self.client.get(reverse('englishTowers:main'))
        self.assertEqual(response.status_code, 200)