from django.urls import path, include
from . import views
from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin

urlpatterns = [
    path('',views.main,name='main'),    # default path
    path('unit/<str:id>', views.unit,name='unit'),
    path('routers',views.routers,name='routers'),
    path('orders',views.orders,name='orders'),
    path('newrental',views.newrental,name='newrental'),
    path('stripePayment',views.stripePayment,name='stripePayment'),
    path('my_stripe_webhook',views.stripe_webhook,name='stripe_webhook'),
    path('jsonresponse',views.json_response,name='json'),
    path('thank_you/', views.thank_you, name='thank_you'),
    path('dashboard/', admin.site.admin_view(views.et_dashboard), name='et_dashboard'),
] + static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)