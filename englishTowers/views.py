from django.shortcuts import render, redirect
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.conf import settings
from django.http import JsonResponse, HttpResponse
from django.core.mail import EmailMultiAlternatives
from django.utils.html import strip_tags
import stripe, json, random, string, logging
from decimal import Decimal

from englishTowers.django_gandalf_client import GandalfAPIClient

logger = logging.getLogger('stripe')
gandalf_client = GandalfAPIClient()
isTest = 1 if settings.STRIPE_MODE == "DEMO" else 0

print(f"isTest: {isTest}")

from django.contrib.auth.decorators import login_required
from django.utils import timezone
from decimal import Decimal

@login_required(login_url='fiberPlan:login')
def et_dashboard(request):
    # Pull data from Gandalf API client
    orders = gandalf_client.get_orders_mtd()
    active_rentals = gandalf_client.get_active_networks()
    recent_orders = gandalf_client.get_recent_orders()

    # Filter out test/live as in your other logic
    isTest = 1 if settings.STRIPE_MODE == "DEMO" else 0
    orders = [o for o in orders if int(o.get('isTest', 0)) == isTest]
    active_rentals = [a for a in active_rentals if int(a.get('isTest', 0)) == isTest]
    recent_orders = [r for r in recent_orders if int(r.get('isTest', 0)) == isTest]

    total_sales = sum(Decimal(o.get('price', 0) or 0) for o in orders)
    num_orders = len(orders)
    num_active = len(active_rentals)
    avg_order_value = (total_sales / num_orders) if num_orders else 0

    context = {
        'total_sales': total_sales,
        'num_orders': num_orders,
        'num_active': num_active,
        'avg_order_value': round(avg_order_value, 2),
        'recent_orders': recent_orders[:5],
    }
    return render(request, 'engtwrs/et_dashboard.html', context)



def thank_you(request):
    return render(request, 'engtwrs/thank_you.html', {})


@login_required(login_url='fiberPlan:login')
def main(request):
    visits = request.session.get('num_visits', 0)
    request.session['num_visits'] = visits + 1
    return render(request, 'engtwrs/main.html', {
        'num_visits': visits,
        'settings': settings
    })

def stripePayment(request):
    return render(request, 'engtwrs/stripe.html', {})

def get_receipt_from_stripe(payment_intent_id):
    try:
        stripe.api_key = settings.STRIPE_SECRET_KEY
        pi = stripe.PaymentIntent.retrieve(payment_intent_id)
        if pi:
            cid = pi.get('latest_charge')
            if cid:
                ch = stripe.Charge.retrieve(cid)
                return getattr(ch, 'receipt_url', None)
    except Exception as e:
        logger.error(f"Stripe receipt error: {e}")
    return None

def email_info_to_customer(email, receipt, key):
    html_content = f"""
    <html>
    <body style="margin:0; padding:0; background:#f6f9fc;">
      <table role="presentation" width="100%" cellspacing="0" cellpadding="0" style="background:#f6f9fc;">
        <tr>
          <td align="center" style="padding:40px 0;">
            <table role="presentation" width="100%" style="max-width:500px; background:#fff; border-radius:14px; box-shadow:0 6px 32px rgba(50,50,93,.07); padding:32px 24px;">
              <tr>
                <td align="center" style="padding-bottom:24px;">
                  <!-- Brand logo or initials -->
                  <div style="background:#0a2540; color:#fff; font-size:32px; font-weight:bold; width:64px; height:64px; border-radius:50%; line-height:64px; margin:0 auto 16px auto;">ESC</div>
                  <h2 style="color:#0a2540; margin:0 0 12px 0; font-size:1.7em; font-weight:600;">Thank you for your purchase!</h2>
                  <p style="color:#425466; margin:0 0 8px 0; font-size:1.15em;">
                    Your payment has been received and your wireless rental is ready.
                  </p>
                </td>
              </tr>
              <tr>
                <td style="padding-bottom:12px;">
                  <p style="font-size:1em; margin:0 0 10px 0;">
                    <strong>Download your receipt:</strong><br>
                    <a href="{receipt}" style="color:#635bff; text-decoration:none; font-weight:bold;">View Receipt</a>
                  </p>
                  <p style="font-size:1em; margin:0 0 10px 0;">
                    <strong>WiFi Password:</strong><br>
                    <span style="font-family:monospace; font-size:1.1em; background:#f6f9fc; padding:2px 8px; border-radius:6px;">{key}</span>
                  </p>
                </td>
              </tr>
              <tr>
                <td align="center" style="padding-top:8px; font-size:0.97em; color:#72798c;">
                  Need help? Email <a href="mailto:<EMAIL>" style="color:#635bff; text-decoration:none;"><EMAIL></a>
                  <br>
                  <div style="margin-top:20px; color:#adb5bd; font-size:0.87em;">
                    &copy; 2025 Eastern Shore Communications
                  </div>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    </body>
    </html>
    """
    text = (
        "Thank you for your purchase!\n"
        "Your payment has been received and your wireless rental is ready.\n"
        f"Download your receipt: {receipt}\n"
        f"WiFi Password: {key}\n"
        "Need help? Email <EMAIL>"
    )
    msg = EmailMultiAlternatives(
        'Your Wireless Rental Receipt & Account Details',
        text,
        to=[email]
    )
    msg.attach_alternative(html_content, 'text/html')
    msg.send()


def email_paylink_to_customer(email: str, paylink: str):
    html_content = f"""
    <html>
    <body style="margin:0; padding:0; background:#f6f9fc;">
      <table role="presentation" width="100%" cellspacing="0" cellpadding="0" style="background:#f6f9fc;">
        <tr>
          <td align="center" style="padding:40px 0;">
            <table role="presentation" width="100%" style="max-width:500px; background:#fff; border-radius:14px; box-shadow:0 6px 32px rgba(50,50,93,.07); padding:32px 24px;">
              <tr>
                <td align="center" style="padding-bottom:24px;">
                  <div style="background:#0a2540; color:#fff; font-size:32px; font-weight:bold; width:64px; height:64px; border-radius:50%; line-height:64px; margin:0 auto 16px auto;">ESC</div>
                  <h2 style="color:#0a2540; margin:0 0 12px 0; font-size:1.7em; font-weight:600;">Complete Your Reservation</h2>
                  <p style="color:#425466; margin:0 0 20px 0; font-size:1.13em;">
                    Thank you for choosing Eastern Shore Wireless Rentals.
                  </p>
                </td>
              </tr>
              <tr>
                <td align="center" style="padding-bottom:18px;">
                  <a href="{paylink}" style="background:#635bff; color:#fff; text-decoration:none; display:inline-block; padding:12px 36px; border-radius:12px; font-size:1.18em; font-weight:bold;">
                    Pay Now
                  </a>
                </td>
              </tr>
              <tr>
                <td align="center" style="font-size:1em; color:#425466;">
                  If the button above doesn't work, paste this link in your browser:<br>
                  <span style="color:#635bff; word-break:break-all;">{paylink}</span>
                </td>
              </tr>
              <tr>
                <td align="center" style="padding-top:20px; font-size:0.97em; color:#72798c;">
                  Need help? Email <a href="mailto:<EMAIL>" style="color:#635bff; text-decoration:none;"><EMAIL></a>
                  <br>
                  <div style="margin-top:20px; color:#adb5bd; font-size:0.87em;">
                    &copy; 2025 Eastern Shore Communications
                  </div>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
    </body>
    </html>
    """
    text_content = (
        "Complete your reservation with Eastern Shore Wireless Rentals.\n"
        f"Pay here: {paylink}\n"
        "Need help? Email <EMAIL>"
    )
    msg = EmailMultiAlternatives(
        'Your English Towers Payment Link',
        text_content,
        to=[email]
    )
    msg.attach_alternative(html_content, 'text/html')
    msg.send()


def generate_password(length=8):
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(length)).lower()

def get_et_redirect_url(request):
    return request.build_absolute_uri('/et/')


@csrf_exempt
def stripe_webhook(request):
    try:
        payload = request.body
        sig_header = request.headers.get("Stripe-Signature")

        # Check if signature header exists
        if not sig_header:
            logger.error("Missing Stripe-Signature header")
            return HttpResponse(status=400)  # Bad request - missing header

        # Check if webhook secret is configured
        if not settings.STRIPE_WEBHOOK_SECRET:
            logger.error("Stripe webhook secret not configured")
            return HttpResponse(status=500)  # Server error - misconfiguration

        try:
            event = stripe.Webhook.construct_event(
                payload, sig_header, settings.STRIPE_WEBHOOK_SECRET
            )
        except stripe.error.SignatureVerificationError as e:
            logger.error(f"Stripe signature verification failed: {str(e)}")
            return HttpResponse(status=400)  # Bad request - invalid signature
        except Exception as e:
            logger.error(f"Stripe webhook construction failed: {str(e)}")
            return HttpResponse(status=500)  # Server error

        try:
            event_type = event.get("type")
            logger.info(f"Webhook event received: {event_type}")
            logger.debug(f"Webhook payload: {json.loads(payload)}")
        except Exception as e:
            logger.error(f"Error extracting event data: {str(e)}")
            return HttpResponse(status=500)  # Server error

        try:
            if event_type == "checkout.session.completed":
                checkout_session = event['data']['object']
                transaction_id = checkout_session.get('payment_intent')
                order_id = checkout_session.get('metadata', {}).get('order_id')
                create_net = bool(
                    checkout_session.get('metadata', {}).get('create_net', False)
                )
                print(f"create_net: {create_net}")
                if not order_id:
                    logger.error("Missing order_id in webhook metadata")
                    return HttpResponse(status=400)  # Bad request - missing required data
                try:
                    customer_email_address = receipt_url = customer_key = customer_data = None
                    receipt_url = get_receipt_from_stripe(transaction_id)
                    logger.info(f"Processing order: {order_id} with transaction: {transaction_id}")
                    # Updated to match current return type
                    response = gandalf_client.update_order(
                        order_id=int(order_id), 
                        transaction_id=transaction_id, 
                        receipt_url=receipt_url, 
                        connect_net=create_net
                    )
                    logger.info(f"Order update response: {response}")
                    
                    # Extract customer data from response if needed
                    customer_data = response  # Adjust based on actual response structure
                    if isinstance(customer_data, dict):
                        customer_email_address = customer_data.get('email')
                        customer_key = customer_data.get('password')
                    elif isinstance(customer_data, (list, tuple)) and len(customer_data) >= 2:
                        customer_email_address = customer_data[0]
                        customer_key = customer_data[1]
                except Exception as e:
                    logger.error(f"Error finalizing order {order_id}: {str(e)}")
                    return HttpResponse(status=500)  # Server error
     
                if customer_data:
                    customer_email_address = customer_data[0]
                    customer_key = customer_data[1]

                    if not customer_email_address:
                        logger.error(f"Missing email address for order: {order_id}")
                        return HttpResponse(status=500)  # Server error

                    try:
                        email_info_to_customer(customer_email_address, receipt_url, customer_key)
                        logger.info(f"Successfully processed order {order_id} for {customer_email_address}")
                    except Exception as e:
                        logger.error(f"Failed to send email for order {order_id}: {str(e)}")
                        return HttpResponse(status=500)  # Server error

                return HttpResponse(status=200)  # Success
            else:
                logger.info(f"Skipped processing of event: {event_type}")
                return HttpResponse(status=200)  # Success - acknowledged but not processed

        except Exception as e:
            logger.error(f"Error processing webhook event {event_type}: {str(e)}")
            return HttpResponse(status=500)  # Server error

    except Exception as e:
        # Top-level exception handler - always return 200 so Stripe doesn't retry
        logger.error(f"Critical error in Stripe webhook: {e}", exc_info=True)
        return HttpResponse(status=200)  # Always return 200 so Stripe doesn't retry



@login_required(login_url='fiberPlan:login')
def newrental(request):
    request.session['num_visits'] = request.session.get('num_visits', 0) + 1
    if request.method != 'POST':
        return render(request, 'engtwrs/main.html', {})
    unit_ = request.POST.get('unit')
    days = int(request.POST.get('days', 0))
    package = request.POST.get('package')
    price = float(request.POST.get('price', 0))
    email = request.POST.get('email')
    password = request.POST.get('password') or generate_password()
    hard_exp = request.POST.get('hard_expiration_date')
    create_net = request.POST.get('network_only') == 'on'
    process_cc = request.POST.get('process_cc') == 'on'
    whereIsPaymentOccuring = request.POST.get('paymentMethod', 'email')  # Default to 'email' if not provided
    redirect_url =  get_et_redirect_url(request)
    after_checkout_redirect_url = request.build_absolute_uri('/et/thank_you/') if whereIsPaymentOccuring == 'email' else redirect_url
    origin = settings.THIS_HOST
    if not (create_net or process_cc):
        messages.error(request, 'Select network or payment', 'alert-danger')
        return redirect(redirect_url)
    try:
        order_res = gandalf_client.new_network(
            unit=unit_,
            password=password, 
            days=days, 
            package=package, 
            email=email, 
            price=price, 
            hard_expiration_date=hard_exp, 
            isTest=isTest, 
            origin=origin,
            create_net=create_net and not process_cc
        )

        order_id = order_res.get('order_id')
        if not order_id:
            raise ValueError('new_network failed')
        if process_cc:
            # Set the Stripe API key before creating the checkout session
            stripe.api_key = settings.STRIPE_SECRET_KEY
            session = stripe.checkout.Session.create(
                payment_method_types=['card'],
                line_items=[{
                    'price_data': {
                        'currency': 'usd',
                        'product_data': {'name': f"{days} Days {package.title()}"},
                        'unit_amount': int(price*100)
                    },
                    'quantity': 1
                }],
                mode='payment', metadata={'order_id': order_id, 'create_net': create_net},
                success_url=after_checkout_redirect_url, cancel_url=after_checkout_redirect_url  #todo change to cancel
            )
            print(f"whereIsPaymentOccuring : {whereIsPaymentOccuring}")
            if whereIsPaymentOccuring == 'email':
                try:
                    logger.info("Email Checkout  Link")
                    email_paylink_to_customer(email, session.url)
                    messages.success(request, 'Payment link has been successfully sent to customer.', 'alert-success')
                except Exception as e:
                    logger.error(f"Error emailing paylink for order {order_id}: {e}")
                    messages.error(request, 'Failed to send payment link email.', 'alert-danger')
                return redirect(redirect_url)
            else:
                # helpdesk mode: immediately redirect to Stripe checkout
                logger.info("Helpdesk Checkout")
                return redirect(session.url)
    except Exception as e:
        logger.error(f"newrental error: {e}")
        messages.error(request, str(e), 'alert-danger')
    return render(request, 'engtwrs/main.html', {})

@login_required(login_url='fiberPlan:login')
def unit(request, id):
    ip = gandalf_client.get_unit_router_ip(id)
    return render(request, 'engtwrs/unit.html', {'ipaddress': ip, 'id': id})

@login_required(login_url='fiberPlan:login')
def routers(request):
    return render(request, 'engtwrs/routers.html', {})

def json_response(request):
    param = request.GET.get('param')
    result = {}
    try:
        match param:
            case 'profile_exists':
                try:
                    res = gandalf_client.security_profile_exists(request.GET.get('unit'))
                    result = res
                except Exception as e:
                    logger.error(f"Error checking security profile: {e}")
                    result = {'exists': False, 'rental_order_info': [], 'error': 'Failed to check security profile'}
            case 'reset_ap_by_unit':
                unit = request.GET.get('unit')
                if unit is None:
                    raise ValueError('Unit is None')
                gandalf_client.reset_ap_by_unit(unit)
                result = {'result': True}
            case 'reset_ap':
                ip = request.GET.get('ipaddress')
                if ip is None:
                    raise    ValueError('IP address is None')
                gandalf_client.reset_access_point(ip)
                # gandalf_client.router_turn_rental_off(ip)  consolidated the above functionality into reset_access_point
                result = {'result': True}
            case 'update_queue':
                ip = request.GET.get('ipaddress')
                queue = json.loads(request.GET.get('queue_json', '{}'))
                gandalf_client.add_queue(ip, queue)
                gandalf_client.save_query('UPDATE `router` SET `queue_name` = %s WHERE `ipaddress` = %s', [queue.get('name'), ip])
                result = {'result': True}
            case 'update_all_queues':
                queue = json.loads(request.GET.get('queue_json', '{}'))
                updated = []
                for r in gandalf_client.get_routers():
                    if r.get('queue_name') != queue.get('name'):
                        gandalf_client.add_queue(r['ipaddress'], queue)
                        info = gandalf_client.get_router_info(r['ipaddress'])
                        gandalf_client.save_router(info)
                        updated.append(r['ipaddress'])
                result = {'updated': updated}
            case 'router_queues':
                result = gandalf_client.get_queue_info(request.GET.get('ipaddress'))
            case 'get_routers':
                result = gandalf_client.get_routers()
            case 'update_all_routers':
                updated = []
                for r in gandalf_client.get_routers():
                    info = gandalf_client.get_router_info(r['ipaddress'])
                    gandalf_client.save_router(info)
                    updated.append(r['ipaddress'])
                result = {'updated': updated}
            case 'get_router_info':
                info = gandalf_client.get_router_info(request.GET.get('ipaddress'))
                gandalf_client.save_router(info)
                result = info
            case 'orders':
                all_orders = gandalf_client.get_orders_mtd()
                if all_orders is None:
                    result = []  # Return empty list instead of None
                else:
                    result = [o for o in all_orders if int(o.get('isTest', 0)) == isTest]
            case 'recent_order_info':
                all_recent = gandalf_client.get_recent_orders()
                if all_recent is None:
                    result = []  # Return empty list instead of None
                else:
                    result = [o for o in all_recent if int(o.get('isTest', 0)) == isTest]
            case 'active':
                all_active = gandalf_client.get_active_networks()
                if all_active is None:
                    result = []  # Return empty list instead of None
                else:
                    result = [o for o in all_active if int(o.get('isTest', 0)) == isTest]
            case 'unit_order':
                result = gandalf_client.get_unit_orders(unit=request.GET.get('unit'))
            case 'router_ssid':
                result = gandalf_client.get_ssid_info(ip=request.GET.get('ipaddress'))
            case 'router_registrations':
                result = gandalf_client.get_registration_list(ip=request.GET.get('ipaddress'))
            case _:
                result = {'result': False}
    except Exception as e:
        logger.error(f"JSON response error: {e}")
        result = {'result': False, 'error': str(e)}
    return JsonResponse(result, safe=False)

from django.views.decorators.cache import never_cache

@never_cache
@login_required(login_url='fiberPlan:login')
def orders(request):
    return render(request, 'engtwrs/main.html', {})

