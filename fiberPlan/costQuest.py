from .models import CostQuest, BroadbandAvailability

class myCostQuest(CostQuest):

    # loads data from filename. county (default Camden)
    def loadData(self,folder, filename, county=37029):
        with open(folder + filename, 'r') as f:
            i = 0
            for line in f.readlines():
                print(line.strip())
                if i > 9:
                    return True
                else:
                    i += 1
