import turtle

# x,y start coordinates
# ports number of splitter output ports
def draw_splitter(x=0, y=0, ports=4):
    tubeLength = 50
    splitterSpacing = 15
    vertLine = (ports - 1) * splitterSpacing
    turtle.speed(0)  # Set the drawing speed to the fastest
    turtle.penup()
    turtle.goto(x,y)
    turtle.pendown()
    turtle.setheading(0)
    turtle.forward(tubeLength)
    turtle.penup()
    turtle.goto(x+tubeLength,y-(vertLine/2))
    turtle.pendown()
    turtle.left(90)
    turtle.forward(vertLine) # draw vertical line
    turtle.right(90)
    newY = y + (vertLine/2)
    for i in range(ports): # draw the FTTH ports
        turtle.forward(tubeLength) # port 1
        turtle.penup()
        newY = newY-(vertLine/(ports-1))
        turtle.goto(x+tubeLength, newY)
        turtle.pendown()
        turtle.write(str(i+1), font=("Arial", 8))

    turtle.hideturtle()

def connect_splitter(ports=4,targetConnectPort=1,targetNumPorts=4,targetX=0,targetY=0):
    tubeLength = 50
    splitterSpacing = 15
    vertLine = (targetNumPorts - 1) * splitterSpacing
    x = targetX + (2 * tubeLength)
    y = targetY + (vertLine/2) - ((targetConnectPort -1)*splitterSpacing)
    draw_splitter(x,y,ports)
    return x,y

# Test the function
splitters = [(0,0,4)]
draw_splitter(0,0,32)
x, y = connect_splitter(4,3,32,0,0)

turtle.done()