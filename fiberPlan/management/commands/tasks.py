from NC811.nc811Class import NC811
from django.core.management import <PERSON><PERSON>ommand, CommandError
from englishTowers.SQLiteClass import MysqlClass
from englishTowers.mtikClass import mtikClass
import nmap

class Command(BaseCommand):
    help = 'Runs NC811 daily tasks. Maybe use Schedule instead? See https://schedule.readthedocs.io/en/stable/'

    def add_arguments(self, parser):
        parser.add_argument(
            "--et-scansubnet",
            action="store_true",
            help="Scan English Towers subnet and update Router IP Addresses"
        )
        parser.add_argument(
            "--et-rentals",
            action="store_true",
            help="Clean up expired rental networks"
        )
        parser.add_argument(
            "--nc811-update-tickets",
            action="store_true",
            help="Pull last 7 days of tickets from NC811 and store locally"
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS(options))
        if options['nc811_update_tickets']:
                nc811_api = NC811()
                nc811_api.refresh_tickets()
                self.stdout.write(self.style.SUCCESS(nc811_api.get_tickets_updated_at()))
                tickets = nc811_api.get_tickets()
                for ticket in tickets:
                    self.stdout.write(self.style.SUCCESS(ticket))
        elif options['et_scansubnet']:
                # scan ET subnet command
                # '***********': {'hostnames': [{'name': '', 'type': ''}], 'addresses': {'ipv4': '***********'}, 'vendor': {}, 'status': {'state': 'up', 'reason': 'syn-ack'}}}}
                # export PYTHONPATH="$PYTHONPATH:./venv/lib/python3.10/site-packages/"
                nm = nmap.PortScanner()
                nm.scan(hosts='*********/23', arguments='-sn')
                hosts_list = [(x,nm[x]['status']['state']) for x in nm.all_hosts()]
                for host, status in hosts_list:
                    mtik = mtikClass()
                    hostname = mtik.get_router_hostname(host)
                    count = 0
                    if hostname != 'Connection Error':
                        count += 1
                        db = MysqlClass()
                        db.update_nas('et'+hostname,host)
                        self.stdout.write(self.style.SUCCESS(count + ': ' + host + ' ' + hostname + ' ' + status))
        elif options['et_rentals']:
                # scan ET subnet command
                self.stdout.write(self.style.SUCCESS("Rental Network Housekeeping tasks"))
                database = MysqlClass()
                results = database.get_active_networks()
                for result in results:
                    self.stdout.write(self.style.SUCCESS(result))