# Generated by Django 5.0.1 on 2024-01-15 20:17

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Vault',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=100)),
                ('lat', models.DecimalField(decimal_places=10, max_digits=12)),
                ('lng', models.DecimalField(decimal_places=10, max_digits=12)),
                ('is_enc', models.BooleanField(default=False)),
                ('is_mini', models.BooleanField(default=False)),
                ('address', models.CharField(max_length=255)),
                ('geolocation', models.<PERSON>r<PERSON><PERSON>(max_length=100)),
                ('notes', models.Char<PERSON><PERSON>(max_length=255)),
            ],
        ),
    ]
