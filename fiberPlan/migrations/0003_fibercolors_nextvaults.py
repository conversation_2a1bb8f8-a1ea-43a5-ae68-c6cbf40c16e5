# Generated by Django 5.0.1 on 2024-01-18 16:12

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fiberPlan', '0002_alter_vault_address'),
    ]

    operations = [
        migrations.CreateModel(
            name='FiberColors',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=15)),
                ('hex', models.<PERSON>r<PERSON>ield(max_length=6)),
            ],
        ),
        migrations.CreateModel(
            name='NextVaults',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dist_to_next_vault', models.IntegerField(verbose_name=10)),
                ('strands', models.IntegerField(verbose_name=3)),
                ('next_vault_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='next_vaults', to='fiberPlan.vault')),
                ('vault_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fiberPlan.vault')),
            ],
        ),
    ]
