# Generated by Django 4.2.11 on 2024-05-15 16:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fiberPlan', '0003_fibercolors_nextvaults'),
    ]

    operations = [
        migrations.CreateModel(
            name='Ticket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=100)),
                ('ticket', models.CharField(max_length=100)),
                ('street', models.CharField(max_length=100)),
                ('cross', models.<PERSON>r<PERSON>ield(max_length=100)),
                ('place', models.<PERSON><PERSON><PERSON><PERSON>(max_length=100)),
                ('work_for', models.Char<PERSON>ield(max_length=100)),
                ('work_type', models.<PERSON><PERSON><PERSON><PERSON>(max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTime<PERSON>ield(auto_now=True)),
            ],
        ),
    ]
