# Generated by Django 4.2.11 on 2024-05-15 19:00

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('fiberPlan', '0005_nc811_rename_work_for_ticket_done_for'),
    ]

    operations = [
        migrations.AddField(
            model_name='ticket',
            name='expires',
            field=models.CharField(default=django.utils.timezone.now, max_length=100),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='ticket',
            name='response_due',
            field=models.CharField(default=django.utils.timezone.now, max_length=100),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='ticket',
            name='update_by',
            field=models.CharField(default=django.utils.timezone.now, max_length=100),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='ticket',
            name='work_date',
            field=models.Char<PERSON>ield(default=django.utils.timezone.now, max_length=100),
            preserve_default=False,
        ),
    ]
