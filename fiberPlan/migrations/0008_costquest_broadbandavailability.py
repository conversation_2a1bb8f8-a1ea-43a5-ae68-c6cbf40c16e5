# Generated by Django 5.0.6 on 2024-08-30 15:21

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fiberPlan', '0007_ticket_priority'),
    ]

    operations = [
        migrations.CreateModel(
            name='CostQuest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('location', models.Char<PERSON>ield(max_length=100)),
                ('address', models.CharField(max_length=100)),
                ('city', models.CharField(max_length=100)),
                ('state', models.CharField(max_length=100)),
                ('zip', models.Char<PERSON>ield(max_length=100)),
                ('county_geoid', models.CharField(max_length=10)),
                ('block_geoid', models.CharField(max_length=50)),
                ('lat', models.DecimalField(decimal_places=10, max_digits=12)),
                ('lng', models.DecimalField(decimal_places=10, max_digits=12)),
                ('fcc_rel', models.Char<PERSON>ield(max_length=10)),
            ],
        ),
        migrations.CreateModel(
            name='BroadbandAvailability',
            fields=[
                ('location', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, serialize=False, to='fiberPlan.costquest')),
                ('technology', models.CharField(max_length=100)),
                ('max_download', models.IntegerField()),
                ('max_upload', models.IntegerField()),
                ('low_latency', models.IntegerField()),
                ('business_residential_code', models.CharField(max_length=1)),
            ],
        ),
    ]
