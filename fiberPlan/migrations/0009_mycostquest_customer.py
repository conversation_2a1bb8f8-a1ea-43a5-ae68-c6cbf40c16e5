# Generated by Django 5.0.6 on 2024-09-02 15:13

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fiberPlan', '0008_costquest_broadbandavailability'),
    ]

    operations = [
        migrations.CreateModel(
            name='myCostQuest',
            fields=[
                ('costquest_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='fiberPlan.costquest')),
            ],
            bases=('fiberPlan.costquest',),
        ),
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('clientId', models.IntegerField()),
                ('firstName', models.CharField(max_length=100)),
                ('lastName', models.CharField(max_length=100)),
                ('email', models.CharField(max_length=100)),
                ('isLead', models.BooleanField(default=False)),
                ('isCustomer', models.BooleanField(default=False)),
                ('street1', models.CharField(max_length=100)),
                ('city', models.CharField(max_length=100)),
                ('location', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='fiberPlan.costquest')),
            ],
        ),
    ]
