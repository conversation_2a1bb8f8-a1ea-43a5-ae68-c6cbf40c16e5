# Generated by Django 5.0.6 on 2024-09-03 13:19

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fiberPlan', '0010_broadbandavailability_id_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='broadbandavailability',
            name='id',
        ),
        migrations.AlterField(
            model_name='broadbandavailability',
            name='location',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, primary_key=True, serialize=False, to='fiberPlan.costquest'),
        ),
    ]
