# Generated by Django 5.0.6 on 2024-09-03 13:26

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fiberPlan', '0011_remove_broadbandavailability_id_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='broadbandavailability',
            name='id',
            field=models.IntegerField(default=1, primary_key=True, serialize=False),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='broadbandavailability',
            name='location',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='fiberPlan.costquest'),
        ),
    ]
