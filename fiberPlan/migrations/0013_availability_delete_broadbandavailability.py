# Generated by Django 5.0.6 on 2024-09-03 13:36

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fiberPlan', '0010_broadbandavailability_id_and_more_squashed_0012_broadbandavailability_id_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Availability',
            fields=[
                ('id', models.IntegerField(primary_key=True, serialize=False)),
                ('technology', models.CharField(max_length=100)),
                ('max_download', models.IntegerField()),
                ('max_upload', models.IntegerField()),
                ('low_latency', models.IntegerField()),
                ('business_residential_code', models.CharField(max_length=1)),
                ('location', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='fiberPlan.costquest')),
            ],
        ),
        migrations.DeleteModel(
            name='BroadbandAvailability',
        ),
    ]
