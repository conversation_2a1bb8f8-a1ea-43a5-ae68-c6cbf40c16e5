# Generated by Django 5.2.1 on 2025-07-31 00:37

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('fiberPlan', '0021_delete_nc811'),
    ]

    operations = [
        migrations.CreateModel(
            name='FiberSignupForm',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('plan_selected', models.TextField(blank=True, null=True)),
                ('install_fee', models.FloatField(blank=True, null=True)),
                ('network_maintenance_fee', models.FloatField(blank=True, null=True)),
                ('modem_lease_fee', models.FloatField(blank=True, null=True)),
                ('router_lease_fee', models.FloatField(blank=True, null=True)),
                ('additional_construction_costs', models.FloatField(blank=True, null=True)),
                ('preferred_installation_date', models.DateField(blank=True, null=True)),
                ('agrees_to_terms', models.Bo<PERSON>anField(default=False)),
                ('signature', models.TextField(blank=True, null=True)),
                ('signed_date', models.DateField(blank=True, null=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='fiberPlan.customer')),
            ],
            options={
                'db_table': 'form_FiberSignupForm',
            },
        ),
        migrations.CreateModel(
            name='SignupToken',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.CharField(max_length=64, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('used', models.BooleanField(default=False)),
                ('expires_at', models.DateTimeField()),
                ('customer', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='fiberPlan.customer')),
            ],
        ),
    ]
