# Generated by Django 5.2.1 on 2025-08-02 12:37

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fiberPlan', '0022_auto_20250730_2037'),
    ]

    operations = [
        migrations.AddField(
            model_name='customer',
            name='email',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='availability',
            name='location',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='fiberPlan.costquest'),
        ),
        migrations.AlterField(
            model_name='customer',
            name='firstName',
            field=models.CharField(max_length=100),
        ),
        migrations.AlterField(
            model_name='customer',
            name='lastName',
            field=models.CharField(max_length=100),
        ),
        migrations.AlterField(
            model_name='nextvaults',
            name='dist_to_next_vault',
            field=models.IntegerField(),
        ),
        migrations.AlterField(
            model_name='nextvaults',
            name='strands',
            field=models.IntegerField(),
        ),
        migrations.AlterModelTable(
            name='customer',
            table='fiberPlan_customer',
        ),
    ]
