from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    # Make sure this matches your latest file name shown in your screenshot
    dependencies = [
        ('fiberPlan', '0023_customer_email_alter_availability_location_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SignupToken',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.CharField(max_length=64, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('used', models.BooleanField(default=False)),
                ('expires_at', models.DateTimeField()),
                ('customer', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='fiberPlan.customer')),
            ],
        ),
    ]
