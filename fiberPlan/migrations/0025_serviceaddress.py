# Generated by Django 5.2.1 on 2025-08-15 16:08

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fiberPlan', '0024_add_signuptoken'),
    ]

    operations = [
        migrations.CreateModel(
            name='ServiceAddress',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('street', models.CharField(max_length=200)),
                ('city', models.CharField(max_length=100)),
                ('state', models.CharField(max_length=50)),
                ('long', models.DecimalField(blank=True, decimal_places=10, max_digits=12, null=True)),
                ('lat', models.DecimalField(blank=True, decimal_places=10, max_digits=12, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_addresses', to='fiberPlan.customer')),
            ],
            options={
                'verbose_name': 'Service Address',
                'verbose_name_plural': 'Service Addresses',
                'db_table': 'fiberPlan_ServiceAddress',
            },
        ),
    ]
