# Generated manually to add new fields to FiberSignupForm

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('fiberPlan', '0027_customer_state'),
    ]

    operations = [
        migrations.AddField(
            model_name='fibersignupform',
            name='plan_price',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='fibersignupform',
            name='protection_plan',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='fibersignupform',
            name='protection_price',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='fibersignupform',
            name='equipment_modem',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='fibersignupform',
            name='equipment_combo',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='fibersignupform',
            name='router_modem_combo_lease_fee',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='fibersignupform',
            name='total_amount',
            field=models.FloatField(blank=True, null=True),
        ),
    ]
