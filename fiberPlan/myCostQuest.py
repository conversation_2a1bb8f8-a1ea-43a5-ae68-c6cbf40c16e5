from .models import CostQuest, Availability
import csv

class myCostQuest(CostQuest):

    # loads data from filename. county (default Camden)
    def loadDataFromCSV(self,folder, filename, county='37029',format='fcc'):
        data_array = []
        i=0
        with open(folder + '/' + filename, 'r') as f:
            csv_reader = csv.reader(f, delimiter=',')
            for row in csv_reader:
                if format == 'fcc':
                    if row[11] == county:
                        data_array.append([row[0],row[1],row[2],row[3],row[4],row[11],row[12],row[14],row[15],row[16]])
                elif format == 'availability':
                    if i == 0:
                        i+=1
                    else:
                        data_array.append([row[2],row[3],row[4],row[5],row[6],row[7]])
        return data_array

    def loadData(self):
        return CostQuest.objects.all().values('location','address','city','state','zip')

    def saveData(self,costquest_data,format='fcc'):
        error_log = []
        # CostQuest data (format = fcc)
        if format.lower() == 'fcc':
            for costquest_record in costquest_data:
                new_costquest = CostQuest.objects.create(
                    location=costquest_record[0],
                    address=costquest_record[1],
                    city=costquest_record[2],
                    state=costquest_record[3],
                    zip=costquest_record[4],
                    county_geoid=costquest_record[5],
                    block_geoid=costquest_record[6],
                    lat=costquest_record[7],
                    lng=costquest_record[8],
                    fcc_rel=costquest_record[9])
                new_costquest.save()
        elif format.lower() == 'availability':
            #ESC Availabilty Data
            for costquest_record in costquest_data:
                #find location from availability data in costquest dataset
                #if this location is not found we should skip the record and log an entry in the error log
                try:
                    cq_location = CostQuest.objects.get(location=costquest_record[0])
                    avail = Availability.objects.create(
                        location=cq_location,
                        technology=costquest_record[1],
                        max_download=costquest_record[2],
                        max_upload=costquest_record[3],
                        low_latency=costquest_record[4],
                        business_residential_code=costquest_record[5])
                    avail.save()
                except CostQuest.DoesNotExist:
                    error_log.append(costquest_record[2])
        return error_log
