<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>ChatGPT Extension</title>
    <style>
        body {
            font-family: Arial,sans-serif;
            padding: 10px;
            width: 400px;
        }
        #user-input {
            width: fit-content;
            padding: 5px;
            margin-bottom: 10px;
        }
        #send-button {
            padding: 5px 10px;
            background-color: green;
            color: white;
            border-radius: 10px;
            border: none;
            cursor: pointer;
        }
        #chat-log{
            border: 1px solid #ccc;
            padding: 10px;
            min-height: 200px;
            max-height: 400px;
            overflow-y: scroll;
        }
        .message {
            margin-bottom: 10px;
        }
        .message .sender{
            font-weight: bold;
            margin-bottom: 5px;
        }
        .message .content {
            margin-left:20px;
        }
    </style>
    <script src="popup.js"></script>
</head>
<body>
    <div>
        <textarea id="user-input" rows="1" placeholder="Enter your question"></textarea>
        <button id="send-button" type="button">Send</button>
    </div>
    <div id="chat-log">
    </div>
</body>
</html>