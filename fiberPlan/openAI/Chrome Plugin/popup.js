const apiKey = '********************************************************************************************************************************************************************';

window.onload = function () {
    document.getElementById('send-button').onclick = sendMessage;
}

async function sendMessage() {
    const endpointUrl = "https://api.openai.com/v1/chat/completions";
    const message = document.getElementById('user-input').value;
    const headers = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${apiKey}`
    }
    const prompt = {
        model: "gpt-3.5-turbo",
        messages: [{
            role: "user",
            content: message + ' please format the output using html tags and wrap in a div format reply using html tags' }],
        temperature: 0.5,
    };
    fetch(endpointUrl, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(prompt),
    })
        .then((response) => response.json())
        .then((data) => {
            const botReply = data.choices[0].message.content;
            appendMessage('User', message);
            appendMessage('Bot', botReply);
        })
        .catch(error => console.log(error));

}
function appendMessage(sender, message) {
    const chatLog = document.getElementById('chat-log');
    const messageElement = document.createElement('div');
    messageElement.classList.add('message');
    messageElement.innerHTML = `<strong>${sender}:</strong>${message}`;
    chatLog.appendChild(messageElement);
    document.getElementById('user-input').value = "";
}