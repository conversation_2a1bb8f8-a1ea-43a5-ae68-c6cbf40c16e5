from openai import OpenAI
from PIL import Image
import io
import base64

client = OpenAI(api_key="********************************************************************************************************************************************************************")

def use_model(client,messages):
    parameters = {
        "model": "gpt-4o",
        "messages": messages,
        "max_tokens": 4000,
    }
    response = client.chat.completions.create(**parameters)
    return response

def encode_image(image_url):
    buffer = io.BytesIO()
    image = Image.open(image_url)
    image.save(buffer, format="PNG")
    encoded_image = base64.b64encode(buffer.getvalue()).decode("utf-8")
    return encoded_image

def assemble_prompt(image_url):
    encoded_image = encode_image(image_url)
    return [
        {
            "role": "system",
            "content": "You are going to create a website from a screenshot."
        },
        {
            "role": "user",
            "content": f"data:image/png;base64,{encoded_image}"
        }
    ]

messages = assemble_prompt("website.png")
response = use_model(client,messages)
print(response)