from django.shortcuts import render
import json
import csv
from django.conf import settings
from django.http import JsonResponse
from django.contrib import messages
from django.contrib.auth.decorators import login_required
import datetime
from django.core.mail import EmailMessage

@login_required(login_url='fiberPlan:login')
def main(request):
    num_visits = request.session.get('num_visits', 0)
    request.session['num_visits'] = num_visits + 1
    context = {'num_visits' : num_visits}
    return render(request, 'openAI/main.html', context)

def json_response(request):
    request.session['num_visits'] = num_visits + 1
    context = {'num_visits': num_visits}
    return render(request, 'openAI/main.html', context)