from django.test import TestCase
from django.urls import reverse
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timedelta
from .models import Customer, SignupToken
from .models import Customer, CostQuest, Vault

class FiberPlanUrlTests(TestCase):
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword'
        )
    
    def test_vaults_view(self):
        """Test that the vaults page loads correctly"""
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('fiberPlan:vaults'))
        self.assertEqual(response.status_code, 200)
    
    def test_map_view(self):
        """Test that the map page loads correctly"""
        self.client.login(username='testuser', password='testpassword')
        response = self.client.get(reverse('fiberPlan:map'))
        self.assertEqual(response.status_code, 200)

    def test_signup_view_with_token(self):
        """Test that the signup view accepts a token parameter"""
        # Create a test customer
        customer = Customer.objects.create(
            clientId=123,
            firstName='Test',
            lastName='Customer',
            street1='123 Test St',
            city='Test City',
            lat=40.7128,
            lng=-74.0060
        )

        # Create a signup token
        token = SignupToken.objects.create(
            customer=customer,
            token='test-token-123',
            expires_at=timezone.now() + timedelta(days=1)
        )

        # Test GET request with token
        response = self.client.get(reverse('fiberPlan:signup_with_token', kwargs={'token': 'test-token-123'}))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Customer')

    def test_signup_view_with_invalid_token(self):
        """Test that the signup view returns 404 for invalid token"""
        response = self.client.get(reverse('fiberPlan:signup_with_token', kwargs={'token': 'invalid-token'}))
        self.assertEqual(response.status_code, 404)

class VaultModelTests(TestCase):
    def test_vault_creation(self):
        """Test creating a basic vault"""
        vault = Vault.objects.create(
            name="Test Vault",
            lat=35.123456,  # Changed from latitude
            lng=-78.654321,  # Changed from longitude
            address="123 Test St",
            is_mini=False,
            is_enc=True,
            notes="Test notes"
        )
        self.assertEqual(vault.name, "Test Vault")
        self.assertEqual(vault.address, "123 Test St")
