from django.urls import path, include
from django.contrib import admin

from . import views

urlpatterns = [
    path('',views.vaults,name='vaults'),    # default path
    path('vault/<int:id>', views.vault,name='vault'),
    path('geocode',views.geocode,name='geocode'),
    path('distance',views.distance,name='distance'),
    path('map',views.map,name='map'),
    path('mydata',views.mydata,name='mydata'),
    path('jsonresponse',views.json_response,name='jsonresponse'),
    path('admin',admin.site.urls,name='admin'),
    path('login/',views.user_login,name='login'),
    path('logout',views.user_logout,name='logout'),
    path('vault/save',views.savevault,name='savevault'),
    path('vault/update',views.updatevault,name='updatevault'),
    path('vault/export',views.export_vaults,name='export_vaults'),
    path('mngt',views.management,name='management'),
    path('mngt/upload_file',views.upload_file,name='upload_file'),
    path('costquest',views.costquest,name='costquest'),
    path('signup/<str:token>/', views.signup, name='signup_with_token'),


]

