from django.db import models
from decimal import Decimal

# class VaultClass
class VaultClass(models.Model):

    def __init__(self):
        self.project_name = "Fiber Plan Vaults"
        self.host = "esvc.us"
        self.user = "ronald"
        self.password = "NpWM3s4xYUhZE6ZQ"
        self.database = "fiberplan"

    # class methods go here
    def get_database(self):
        try:
            mydb = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database,
            )
            return mydb
        except mysql.connector.Error as err:
            print(err)

    def get_vaults(self):
        result = Vault.objects.all()
        return result

    def save_vault(self, vault):
        new_vault = Vault.objects.create(
            name=vault.name,
            notes=vault.notes,
            lat=vault.lat,
            lng=vault.lng,
            address=vault.address,
            is_enc=vault.is_enc,
            is_mini=vault.is_mini,
        )
        new_vault.save()
        return True

    def update_vault(self, vault):
        v = Vault(
            name=vault[1],
            lat=vault[2],
            lng=vault[3],
            is_enc=vault[4],
            is_mini=vault[5],
            notes="na",
            address=vault[7],
        )
        v.save()

    def get_vault_by_id(self,vaults,id):
        for vault in vaults:
            if vault[0] == id:
                return vault

    def get_vault_id(self,vault):
        return vault[0]

    def get_vault_name(self,vault):
        return vault[1]

    def set_vault_name(self,vault,name):
        vault = list(vault)
        vault[1] = name
        vault = tuple(vault)
        self.update_vault(vault)

    def get_vault_location(self,vault):
        location = [vault[2],vault[3]]
        return location

    def get_vault_location_lat(self,vault):
        return vault[2]

    def get_vault_location_lng(self,vault):
        return vault[3]

    def get_vault_is_mini(self,vault):
        return vault[4]==1

    def get_vault_is_enc(self,vault):
        return vault[5]==1

    def get_vault_notes(self,vault):
        return vault[6]==1

    def get_vault_address(self,vault):
        return vault[7]


vault_class = VaultClass(Vault)
my_loop = True
while my_loop:
    vaults = vault_class.get_vaults()
    for vault in vaults:
        if vault.is_enc():
            print(vault.id(),': ',vault.name())
    vault_id = int(input("edit which vault? Enter ID:"))
    if vault_id == 0:
        my_loop = False
    else:
        prompt = "New Name for :" + vault.name() + ":"
        name = input(prompt)
        vault_class.set_vault_name(vault.id(),name)



