#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os.path
import pkgutil
import shutil
import sys
import struct
import tempfile

# Minimum supported python version
MIN_SUPPORTED_PYTHON = (3, 7)


def bootstrap(tmpdir=None):
    # type: (str | None) -> None
    """
    Bootstrap pip into the current Python installation.
    """
    if sys.version_info < MIN_SUPPORTED_PYTHON:
        raise SystemExit(
            "pip {} requires Python {} or later".format(
                pip_version, ".".join(map(str, MIN_SUPPORTED_PYTHON))
            )
        )

    # Ensure we're installing wheel
    packages = ["pip", "setuptools", "wheel"]

    try:
        import ensurepip

        ensurepip.bootstrap(
            root=tmpdir,
            upgrade=True,
            altinstall=False,
            default_pip=True,
            verbosity=0,
        )
    except ImportError:
        # ensurepip is not available, use the embedded copy
        import runpy
        import site

        # Detect if we're running as a 32 or 64 bit process
        is_64bits = struct.calcsize("P") == 8

        # Use the embedded copy of pip provided in this file
        pip_zip = pkgutil.get_data("__main__", "pip.zip")
        with tempfile.NamedTemporaryFile(suffix=".zip") as fp:
            fp.write(pip_zip)
            fp.flush()
            sys.path.insert(0, fp.name)
            runpy.run_module("pip", run_name="__main__")

    # Add the newly installed pip to PATH
    if tmpdir:
        location = os.path.join(tmpdir, "bin")
        if not os.path.exists(location):
            location = os.path.join(tmpdir, "Scripts")
        if not os.path.exists(location):
            location = None
        if location:
            import glob

            pip_exe = glob.glob(os.path.join(location, "pip*"))[0]
            shutil.copy(pip_exe, os.path.join(os.path.dirname(sys.executable), "pip"))


def main():
    tmpdir = tempfile.mkdtemp()
    try:
        bootstrap(tmpdir=tmpdir)
    finally:
        shutil.rmtree(tmpdir, ignore_errors=True)


if __name__ == "__main__":
    main()