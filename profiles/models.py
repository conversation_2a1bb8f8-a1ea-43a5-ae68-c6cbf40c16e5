from django.db import models
from django.contrib.auth.models import User

class Profile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    bio = models.TextField(blank=True)
    location = models.CharField(max_length=100, blank=True)
    birth_date = models.DateField(null=True, blank=True)

    def __str__(self):
        return self.user.username


    def get_dirty_fields(self):
        """
        Compares the current instance to its saved state and
        returns a dictionary of changed fields with old and new values.
        """
        dirty_fields = {}
        if not self.pk:
            return dirty_fields  # New instance, no changes to compare

        saved_instance = Profile.objects.get(pk=self.pk)

        for field in self._meta.fields:
            field_name = field.name
            old_value = getattr(saved_instance, field_name)
            new_value = getattr(self, field_name)

            if old_value != new_value:
                dirty_fields[field_name] = {'old': old_value, 'new': new_value}

        return dirty_fields