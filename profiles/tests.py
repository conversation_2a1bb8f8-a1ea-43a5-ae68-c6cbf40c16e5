from django.test import TestCase
from django.urls import reverse
from django.contrib.auth.models import User
from django.contrib.auth import get_user_model

class ProfilesTests(TestCase):
    def setUp(self):
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword',
            email='<EMAIL>'
        )
    
    def test_login_view(self):
        """Test that the login page loads correctly"""
        # Use the correct URL name with namespace
        response = self.client.get(reverse('fiberPlan:login'))
        self.assertEqual(response.status_code, 200)
    
    def test_user_login(self):
        """Test user login functionality"""
        login_successful = self.client.login(
            username='testuser', 
            password='testpassword'
        )
        self.assertTrue(login_successful)
