from django.urls import path
from django.contrib.auth import views as auth_views
from . import views
from .views import CustomPasswordChangeView

urlpatterns = [
    path('profile/', views.profile_view, name='profile_view'),
    path('profile/edit/', views.profile_edit, name='profile_edit'),
    path('password_change/', CustomPasswordChangeView.as_view(), name='password_change'),
    path('password_change/done/', auth_views.PasswordChangeDoneView.as_view(
        template_name='profiles/password_change_done.html'
    ), name='password_change_done'),
    path('groups/', views.group_list, name='group_list'),
    path('groups/add/', views.group_create, name='group_create'),
    path('groups/<int:group_id>/edit/', views.group_edit, name='group_edit'),
    path('groups/<int:group_id>/delete/', views.group_delete, name='group_delete'),
    path('manage-users/', views.manage_user_groups_permissions, name='manage_user_groups_permissions'),
    path('audits/', views.get_audits, name='audit_view'),
    path('audits/json/', views.audit_json, name='audit_json'),
    path('add_user/', views.add_user, name='add_user'),
    path('change_user_password/', views.change_user_password, name='change_user_password'),
    path('delete_user/', views.delete_user, name='delete_user'),
    path('leads/', views.lead_management, name='lead_management'),
    path('leads/<int:id>/edit/', views.edit_lead, name='edit_lead'),
    path('leads/<int:id>/delete/', views.delete_lead, name='delete_lead'),
    path('leads/<int:id>/process/', views.process_signup, name='process_signup'),
    path('leads/add/', views.add_lead, name='add_lead'),
    path('api/geocode/', views.geocode_address, name='geocode_address'),

]
