from datetime import datetime, timedelta

# Define a constant for locations
LOCATIONS = ['wts', 'off', 'ccp', 'wtn', 'wl']


class DeviceManager:
    def get_devices_by_status(self, location, status):
        """
        General helper method to get devices by specific statuses (e.g., active/inactive).
        """
        devices = self.get_devices(location)
        statuses = status if isinstance(status, list) else [status]
        result = []

        for device in devices['description']['endpoints']:
            if device['status'] in statuses:
                reason = None
                if device['status'] in ['inactive', 'disconnected']:
                    reason = 'Suspended' if device.get('suspended', False) else 'Offline'
                result.append({
                    'name': device['name'],
                    'status': device['status'],
                    'reason': reason,  # Only populated for inactive devices
                    'updated': device['updated'],
                    'location': location
                })
        return result

    def get_all_devices_by_status(self, status):
        """
        Get all devices across locations by a specific status (active/inactive).
        """
        result = []
        for location in LOCATIONS:
            devices = self.get_devices_by_status(location, status)
            for device in devices:
                updated_time = datetime.strptime(device['updated'][:19], '%Y-%m-%dT%H:%M:%S') - timedelta(hours=5)
                delta = self.time_diff(device['updated'])[:-7]

                result.append({
                    'name': device['name'],
                    'status': device['status'],
                    'reason': device.get('reason'),
                    'updated': updated_time.strftime('%m-%d-%Y %H:%M:%S'),
                    'delta_time': delta,
                    'location': location
                })
        return result

    # Wrapper methods for convenience
    def get_inactive_devices_by_location(self, location='wtn'):
        return self.get_devices_by_status(location, ['inactive', 'disconnected'])

    def get_active_devices_by_location(self, location='wtn'):
        return self.get_devices_by_status(location, 'active')

    def get_all_inactive_devices(self):
        return self.get_all_devices_by_status(['inactive', 'disconnected'])

    def get_all_active_devices(self):
        return self.get_all_devices_by_status('active')
