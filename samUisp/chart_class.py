from samUisp.uispClass import UISP
#from graphos.sources.simple import SimpleDataSource
#from graphos.renderers.gchart import LineChart
import datetime

class HSChart():
    def new_graph(self, location):
        sam = UISP()
        results = sam.get_traffic(location)
        
        # Handle case where get_traffic returns None
        if results is None:
            # Return a default/empty chart or raise a meaningful error
            # For testing, we'll create a minimal data structure
            return None
        
        period = results['period']
        start_time = results['interval']['start']
        end_time = results['interval']['end']
        data = []
        data.append(['Time', 'Download', 'Upload'])
        for i in range(0, 59):
            est_time = datetime.datetime.fromtimestamp((results['download']['avg'][i]['x'] - (4 * 3600000)) / 1000)
            if('y' in results['download']['avg'][i]):
                y_dl = results['download']['avg'][i]['y']
            else:
                y_dl = 0
            if ('y' in results['upload']['avg'][i]):
                y_ul = results['upload']['avg'][i]['y']
            else:
                y_ul = 0
            dl = [est_time.strftime('%H:%M:%S'), y_dl / 1000000,
                  y_ul / 1000000]
            data.append(dl)
        # DataSource object
        data_source = SimpleDataSource(data=data)
        # Options
        chart_options = {
            'title': 'Throughput '+ location + ' in last hour',
            'hAxis': {
                'title': 'Time',
                'textStyle': {
                    'color': '#01579b',
                    'fontSize': 10,
                    'fontName': 'Arial',
                    'bold': 'true',
                    'italic': 'true'
                },
                'titleTextStyle': {
                    'color': '#01579b',
                    'fontSize': 12,
                    'fontName': 'Arial',
                    'bold': 'true',
                    'italic': 'true'
                }
            },
            'vAxis': {
                'title': 'Bandwidth (Mbps)',
                'textStyle': {
                    'color': '#1a237e',
                    'fontSize': 10,
                    'bold': 'false'
                },
                'titleTextStyle': {
                    'color': '#1a237e',
                    'fontSize': 12,
                    'bold': 'true'
                }
            },
            'colors': ['#a52714', '#097138']
        }
        # Chart object
        chart = LineChart(data_source, options=chart_options)
        return chart