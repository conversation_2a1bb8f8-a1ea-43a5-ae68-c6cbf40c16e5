<?php

class UcrmApiAccess {

    const API_URL = 'https://esvc.uisp.com/api/v1.0';
    const NSM_URL = 'https://esvc.uisp.com/nms/api/v2.1';
    const NSM_KEY = 'fc4b4ca5-06fc-4639-9e01-2643b58cbb57';
    const APP_KEY = '5G8nC5Iz7EQOAE/yhZBDhCFtrjTqZKOk3wh+Hj900+TOSPCk2TB995cOBfou9iUB';

    /**
     * @param string $url
     * @param string $method
     * @param array  $post
     *
     * @return array|null
     */
    public static function doRequest($framework = 'CRM', $url, $method = 'GET', $post = []) {
        $method = strtoupper($method);
        $framework = strtoupper($framework);

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_TIMEOUT, 90);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, false);

        switch ($framework) {
            case 'NSM':
                curl_setopt(
                        $ch,
                        CURLOPT_URL,
                        sprintf(
                                '%s/%s',
                                self::NSM_URL,
                                $url
                        )
                );
                curl_setopt(
                        $ch,
                        CURLOPT_HTTPHEADER,
                        [
                            'accept: application/json',
                            sprintf('X-Auth-Token: %s', self::NSM_KEY),
                        ]
                );
                break;
            case 'CRM':
                curl_setopt(
                        $ch,
                        CURLOPT_URL,
                        sprintf(
                                '%s/%s',
                                self::API_URL,
                                $url
                        )
                );
                curl_setopt(
                        $ch,
                        CURLOPT_HTTPHEADER,
                        [
                            'Content-Type: application/json',
                            sprintf('X-Auth-App-Key: %s', self::APP_KEY),
                        ]
                );
                break;
        }

        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
        } elseif ($method !== 'GET') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        }

        if (!empty($post)) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post));
        }
        //curl_setopt($ch, CURLINFO_HEADER_OUT, true);
        $response = curl_exec($ch);
        //$headerSent = curl_getinfo($ch, CURLINFO_HEADER_OUT );
        //print_r($headerSent);

        if (curl_errno($ch) !== 0) {
            echo sprintf('Curl error: %s', curl_error($ch)) . PHP_EOL;
        }

        if (curl_getinfo($ch, CURLINFO_HTTP_CODE) >= 400) {
            echo sprintf('API error: %s', $response) . PHP_EOL;
            $response = false;
        }

        curl_close($ch);

        return $response !== false ? json_decode($response, true) : null;
    }

    public function doSearch($array, $key, $value) {
        $results = array();

        if (is_array($array)) {
            if (isset($array[$key]) && $array[$key] == $value) {
                $results[] = $array;
            }

            foreach ($array as $subarray) {
                $results = array_merge($results, UcrmApiAccess::doSearch($subarray, $key, $value));
            }
        }

        return $results;
    }

}

function writeToLog($log) {
        file_put_contents('./log_uisp_php'.'.log', $log, FILE_APPEND);
        return;
    }

function createFiles($location,$customers,$clients,$services,$servicePlans) {
    $file1 = "user,down,up\r\n";
        $file2 = "IP,user,plan\r\n";
        $file3 = "user,plan,down,up,ip\r\n";
        foreach ($customers as $customer) {
            $clientId = $customer['ucrm']['client']['id'];
            $serviceId = $customer['ucrm']['service']['id'];
            $clientName = $customer['ucrm']['client']['name'];
            $clientIp = array();
            if (!empty($customer['description']['ipAddresses'])) {
                $clientIp[] = $customer['description']['ipAddresses'][0];
                if (count($customer['description']['ipAddresses']) > 1) {
                    for ($i = 1; $i < count($customer['description']['ipAddresses']); $i++) {
                        $ip = explode('.', $customer['description']['ipAddresses'][$i]);
                        if ($ip[0] != '172') {
                            $clientIp[] = $customer['description']['ipAddresses'][$i];
                        }
                    }
                }
            }
            $clientServicePlan = str_replace(array(' ', '/', '&'), '_', $customer['ucrm']['service']['name']);
            $service = UcrmApiAccess::doSearch($services, 'clientId', $clientId);
            foreach ($services as $service) {
                if ($service['clientId'] == $clientId) {
                    foreach ($servicePlans as $plan) {
                        if ($plan['id'] == $service['servicePlanId']) {
                            $down = $plan['downloadSpeed'] * 1000;
                            $up = $plan['uploadSpeed'] * 1000;
                            foreach ($clients as $client) {
                                if ($client['id'] == $clientId) {
                                    $clientName = str_replace(array(' ','/','&',',','-','.'), '_', $clientName);
                                    $clientName = str_replace(array('__','___'), '_', $clientName);
                                    if (!empty($clientIp)) {
                                        foreach ($clientIp as $ip) {
                                            echo $clientName . '-' . $serviceId . ',' . $down . ',' . $up . ',' . $ip . ',' . $clientServicePlan . PHP_EOL;
                                            $file1 .= $clientName . '-' . $serviceId . ',' . $down . ',' . $up . "\r\n";
                                            $file2 .= $ip . ',' . $clientName . '-' . $serviceId . ',' . $clientServicePlan . "\r\n";
                                            $file3 .= $clientName . '-' . $serviceId . ',' . $clientServicePlan . ',' . $down . ',' . $up . ',' . $ip . PHP_EOL;
                                        }
                                    }
                                    break;
                                }
                            }
                            break;
                        }
                    }
                    break;
                }
            }
        }
        $filename = '/home/<USER>/' . $location . 'file';
        file_put_contents($filename . '1', $file1);
        file_put_contents($filename . '2', $file2);
        file_put_contents($filename . '3', $file3);
    return true;
}

// Setting unlimited time limit (updating lots of clients can take a long time).
set_time_limit(0);
// Start output buffering
ob_start();
// Init Log
writeToLog(date("j.n.Y H:i"). PHP_EOL);
//
if (php_sapi_name() !== 'cli') {
    echo '<pre>';
}

if (isset($argc)) {
    for ($i = 0; $i < $argc; $i++) {
        writeToLog("Argument #" . $i . " - " . $argv[$i] . PHP_EOL);
    }
}

// Pull data from SAM
$devices = UcrmApiAccess::doRequest('NSM', 'devices/ips') ?: [];
writeToLog(sprintf('Found %d IPs.', count($devices)) . PHP_EOL);
$clients = UcrmApiAccess::doRequest('CRM', 'clients') ?: [];
writeToLog(sprintf('Found %d clients.', count($clients)) . PHP_EOL);
$servicePlans = UcrmApiAccess::doRequest('CRM', 'service-plans') ?: [];
writeToLog(sprintf('Found %d Service Plans.', count($servicePlans)) . PHP_EOL);
$services = UcrmApiAccess::doRequest('CRM', 'clients/services') ?: [];
writeToLog(sprintf('Found %d Services.', count($services)) . PHP_EOL);
// flush output buffer
ob_flush();
//
switch (strtoupper($argv[1])) {
    case 'CAMDEN':
        $data = array('id' => 'd68366e9-2aa6-44cc-a231-85ab5fa0d427');
        $wtn = 'b7cda07e-decc-40b0-b7c6-6897135a5645';
        $ccp = '1a782c6d-657f-4a5b-8a46-c3260a114281';
        $off = '79740732-3b6b-48d3-8a18-7559da9bef4e';
        $wts = '6725279f-051f-4977-8a74-ac0f3322c21b';
        $camdenWTN = UcrmApiAccess::doRequest('NSM', 'sites' . '/' . $wtn, 'GET');
        $camdenWTS = UcrmApiAccess::doRequest('NSM', 'sites' . '/' . $wts, 'GET');
        $camdenOFF = UcrmApiAccess::doRequest('NSM', 'sites' . '/' . $off, 'GET');
        $camdenCCP = UcrmApiAccess::doRequest('NSM', 'sites' . '/' . $ccp, 'GET');
        $camdenDevices = array_merge($camdenWTN['description']['endpoints'], $camdenCCP['description']['endpoints'],
                $camdenOFF['description']['endpoints'], $camdenWTS['description']['endpoints']);
        $camdenCustomers = array();
        foreach ($camdenDevices as $device) {
            $deviceId = $device['id'];
            $customer = UcrmApiAccess::doRequest('NSM', 'sites' . '/' . $deviceId, 'GET');
            $camdenCustomers[] = $customer;
        }
        writeToLog(sprintf('Found %d Camden Customers.', count($camdenCustomers)) . PHP_EOL);
        // Get collection of all Clients.
        createFiles('camden',$camdenCustomers,$clients, $services, $servicePlans);
        break;
    case "MOYOCK":
        // Moyock
        $file1 = "user,down,up\r\n";
        $file2 = "IP,user,plan\r\n";
        $file3 = "user,plan,down,up,ip\r\n";
        $moy = 'eef2fd19-a2b5-499e-ad5c-2949bb30289d';
        $moyockTWR = UcrmApiAccess::doRequest('NSM', 'sites' . '/' . $moy, 'GET');
        $moyockDevices = $moyockTWR['description']['endpoints'];
        $moyockCustomers = array();
        foreach ($moyockDevices as $device) {
            $deviceId = $device['id'];
            $customer = UcrmApiAccess::doRequest('NSM', 'sites' . '/' . $deviceId, 'GET');
            $moyockCustomers[] = $customer;
        }
        echo sprintf('Found %d Moyock Customers.', count($moyockCustomers)) . PHP_EOL;
        createFiles('moyock',$moyockCustomers,$clients, $services, $servicePlans);
        break;
}
//foreach ($servicePlans as $plan) {
//      echo 'plan:' .  $plan['id'] . ' Down:' .$plan['downloadSpeed'] . ' Up:' . $plan['uploadSpeed'] . PHP_EOL;
//}
// Go through all Clients and update them.
// In this case we are updating all invoice options to use system defaults.
/* foreach ($clients as $client) {
  $response = UcrmApiAccess::doRequest(
  sprintf('clients/%d', $client['id']),
  'PATCH',
  [
  'sendInvoiceByPost' => null,
  'invoiceMaturityDays' => null,
  'stopServiceDue' => null,
  'stopServiceDueDays' => null,
  ]
  );

  if ($response !== null) {
  echo sprintf('Client ID %d successfully updated.', $client['id']) . PHP_EOL;
  } else {
  echo sprintf('There was an error in updating client ID %d.', $client['id']) . PHP_EOL;
  }
  }
 */

writeToLog('Done.' . PHP_EOL);
// and one last output buffer flush
ob_end_flush();