from django.shortcuts import render
import json
import csv
from django.conf import settings
from django.http import JsonResponse
from django.contrib import messages
from samUisp.uispClass import UISP
from django.contrib.auth.decorators import login_required
from fiberPlan.models import Customer, CostQuest
import datetime
from samUisp.chart_class import H<PERSON>hart
import googlemaps
from django.core.mail import EmailMessage
from englishTowers.django_gandalf_client import GandalfAPIClient

gandalf_client = GandalfAPIClient()


@login_required(login_url='fiberPlan:login')
def main(request):
    num_visits = request.session.get('num_visits', 0)
    request.session['num_visits'] = num_visits + 1
    context = {'num_visits': num_visits}
    return render(request, 'samUisp/main.html', context)


@login_required(login_url='fiberPlan:login')
def financials(request):
    context = {}
    return render(request, 'samUisp/finances.html', context)


@login_required(login_url='fiberPlan:login')
def customers(request):
    context = {}
    return render(request, 'samUisp/customers.html', context)


@login_required(login_url='fiberPlan:login')
def charts(request):
    mychart = HSChart()

    # Create charts and handle None returns
    chart_wtn = mychart.new_graph('wtn') or create_empty_chart('wtn')
    chart_ccp = mychart.new_graph('ccp') or create_empty_chart('ccp')
    chart_off = mychart.new_graph('off') or create_empty_chart('off')
    chart_wts = mychart.new_graph('wts') or create_empty_chart('wts')

    context = {'chart_wts': chart_wts, 'chart_off': chart_off, 'chart_ccp': chart_ccp, 'chart_wtn': chart_wtn}
    return render(request, 'samUisp/charts.html', context)


def create_empty_chart(location):
    # Create a minimal chart for testing/error cases
    from graphos.sources.simple import SimpleDataSource
    from graphos.renderers.gchart import LineChart

    data = [['Time', 'Download', 'Upload'],
            ['00:00:00', 0, 0]]

    data_source = SimpleDataSource(data=data)
    chart_options = {
        'title': f'No data available for {location}',
        'hAxis': {'title': 'Time'},
        'vAxis': {'title': 'Bandwidth (Mbps)'},
    }
    return LineChart(data_source, options=chart_options)


#
# support functions without templates


def json_response(request):
    param = request.GET.get('param', 'empty')
    result = []
    match param:
        case 'ping_olts':
            # list out your devices
            olts = [
                {'name': 'NC-CMD-WTN-OLT1', 'ip': '************', 'ping': ''},
                {'name': 'NC-CMD-WTN-OLT2', 'ip': '************', 'ping': ''},
                {'name': 'NC-CMD-CCP-OLT1', 'ip': '************', 'ping': ''},
                {'name': 'NC-CMD-CCP-OLT2', 'ip': '************', 'ping': ''},
                {'name': 'NC-CMD-WL-OLT1' , 'ip': '***********', 'ping': ''},
                {'name': 'NC-CMD-OFF-OLT1', 'ip': '************', 'ping': ''},
                {'name': 'NC-CMD-WTS-OLT1', 'ip': '************', 'ping': ''},
                {'name': 'NC-EC-HUT-EDGEMAX', 'ip': '************', 'ping': ''},
                {'name': 'NC-CMD-CLIENT-SECVA', 'ip': '**************', 'ping': ''},
            ]

            # extract just the IPs and ping them all at once
            ip_list = [item['ip'] for item in olts]

            ping_map = gandalf_client.ping_hosts(ip_list)
            print(f"ping_map: {ping_map}")

            # update each entry's 'ping' field
            for olt in olts:
                olt['ping'] = ping_map.get(olt['ip'], '')

            result = olts
        case 'get_device_statistics':
            sam = UISP()
            result = sam.get_device_statistics()
        case 'get_onus_one_hour':
            # attributes.parentId
            sam = UISP()
            result = sam.get_onus_one_hour()
        case 'get_olt_by_id':
            sam = UISP()
            result = sam.get_olt_by_id(request.GET.get('id'))
        case 'get_onus':
            sam = UISP()
            result = sam.get_onus('off_olt1')
        case 'devices':
            sam = UISP()
            result = sam.get_devices('wtn')
        case 'inactiveDevices':
            sam = UISP()
            # result = sam.get_inactive_devices_by_location()
            result = sam.get_all_devices_by_status('inactive')
        case 'allInactiveDevices':
            # time in zulu e.g. 2024-05-30T15:00:18.917Z
            sam = UISP()
            # result = sam.get_all_inactive_devices()
            result = sam.get_all_devices_by_status('inactive')
        case 'allOfflineDevices':
            # time in zulu e.g. 2024-05-30T15:00:18.917Z
            sam = UISP()
            # result = sam.get_all_offline_devices()
            result = sam.get_all_devices_by_status('disconnected')
        case 'timediff':
            sam = UISP()
            result = sam.time_diff('2024-05-31T13:31:59.307Z')
        case 'allActiveDevices':
            sam = UISP()
            # result = sam.get_all_active_devices()
            result = sam.get_all_devices_by_status('active')
        case 'get_traffic':
            sam = UISP()
            results = sam.get_traffic(request.GET.get('location', 'empty'))
            period = results['period']
            start_time = results['interval']['start']
            end_time = results['interval']['end']
            result = []
            result.append(['Time', 'DL(mbps)', 'UL(mbps)'])
            for i in range(0, 59):
                dl = [datetime.datetime.fromtimestamp(results['download']['avg'][i]['x'] / 1000).strftime('%c'),
                      results['download']['avg'][i]['y'] / 1000000, results['upload']['avg'][i]['y'] / 1000000]
                result.append(dl)
        case 'client':
            sam = UISP()
            clients = sam.do_request('CRM', '/clients')
            for client in clients:
                # fp_client = Customer.objects.get(clientId=client['id'])
                # fp_client.lat = client['lat']
                # fp_client.lng = client['lng']
                # fp_client.save()

                fp_customer = Customer.objects.create(
                    clientId=client['contacts'][0]['clientId'],
                    firstName=client['firstName'] if client['firstName'] is not None else 'None',
                    lastName=client['lastName'] if client['lastName'] is not None else 'None',
                    street1=client['street1'] if client['street1'] is not None else 'Unknown',
                    city=client['city'] if client['city'] is not None else 'Unknown',
                    isLead=client['isLead'],
                    lat=client['addressGpsLat'],
                    lng=client['addressGpsLon'],
                    isCustomer=True if client['clientType'] == 1 else False)
                fp_customer.save()
        case 'servicePlans':
            sam = UISP()
            # service plan group ids
            # Camden FTTH: 71b32379-5281-4903-883f-a4d3dc13b032
            # Camden FTTB: 46712e88-9b68-449c-adaa-cd7ea659457f
            # Camden DIA: cc8c44e8-bc75-4401-afe6-4ac12d78492d
            # Camden Non Fiber: 953796d2-2868-4440-bdb9-d323e030ba32
            # service_plans = sam.do_request('CRM', '/service-plans/3')
            # service_plans = sam.do_request('CRM', '/invoices/3')
            service_plans = sam.do_request('CRM',
                                           '/payments?createdDateFrom=2024-01-01&createdDateTo=2024-12-31&clientId=505')
            # service_plans = sam.do_request('CRM','/service-plans/statistics')
            # service_plans = sam.do_request('CRM','/surcharges')
            return JsonResponse(service_plans, safe=False)
        case "activeCustomers":
            sam = UISP()
            uisp_data = sam.get_customers_by_status(status='customer')
            output = sync_customer_data(uisp_data, is_customer=True)
            print(output)
            result = uisp_data
        case "customerLeads":
            sam = UISP()
            uisp_data = sam.get_customers_by_status(status='lead')
            output = sync_customer_data(uisp_data, is_customer=False)
            print(output)
            result = uisp_data
        case "customerLeads":
            sam = UISP()
            result = sam.get_customers_by_status(status='lead')
        case 'clients':
            sam = UISP()
            clients = sam.do_request('CRM', '/clients')
            services = sam.do_request('CRM', '/clients/services')
            outstanding = 0
            client_services = []
            my_csv = []
            camden_zip = ['27921', '27974', '27976']
            for service in services:
                client_services.append({
                    'clientId': service['clientId'],
                    'service_name': service['name'],
                    'service_totalPrice': service['totalPrice']})
            for client in clients:
                if client['clientType'] == 1 and client['isLead'] == False and client['zipCode'] in camden_zip:
                    recurring = 0
                    for service in client_services:
                        if service['clientId'] == client['contacts'][0]['clientId']:
                            recurring += service['service_totalPrice']
                    result.append({'clientId': client['contacts'][0]['clientId'],
                                   'firstName': client['firstName'],
                                   'lastName': client['lastName'],
                                   'street1': client['street1'],
                                   'city': client['city'],
                                   'zipCode': client['zipCode'],
                                   'email': client['contacts'][0]['email'],
                                   'hasOutage': client['hasOutage'],
                                   'hasSuspendedService': client['hasSuspendedService'],
                                   'outstanding': "{:,.2f}".format(client['accountOutstanding']),
                                   'recurring': "{:,.2f}".format(recurring),
                                   'fullAddress': client['fullAddress'], })
                    my_csv.append(
                        [client['contacts'][0]['clientId'], client['firstName'], client['lastName'], client['street1'],
                         client['city'], client['zipCode'], "{:,.2f}".format(recurring)])
                    outstanding += client['accountOutstanding']
            with open('camden_customers.csv', 'w', newline='') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerows(my_csv)
            result.append({'outstanding': "{:,.2f}".format(outstanding), })
        case _:
            result = {'result': False}
    return JsonResponse(result, safe=False)


def sync_customer_data(uisp_data, is_customer=True):
    """
    Synchronize customer/lead data between UISP and local database
    Args:
        uisp_data: List of customer/lead data from UISP
        is_customer: Boolean indicating if these are customers (True) or leads (False)
    Returns:
        Dict with sync results
    """
    # Get existing records of this type from database
    existing_ids = set(Customer.objects.filter(isCustomer=is_customer).values_list('clientId', flat=True))

    # Get all IDs from UISP response
    uisp_ids = set(customer['clientId'] for customer in uisp_data)

    # Find records to delete (in database but not in UISP)
    ids_to_delete = existing_ids - uisp_ids
    deleted_count = Customer.objects.filter(clientId__in=ids_to_delete, isCustomer=is_customer).delete()[0]

    # Track statistics
    updated_count = 0
    created_count = 0
    error_count = 0

    # Process each record from UISP
    gmaps = googlemaps.Client(key=settings.GOOGLE_API_KEY)

    for uisp_record in uisp_data:
        # Try to get geocoding data for the address
        address = f"{uisp_record['street1']}, {uisp_record['city']}"
        try:
            geocode_result = gmaps.geocode(address)
            if geocode_result:
                lat = geocode_result[0]['geometry']['location']['lat']
                lng = geocode_result[0]['geometry']['location']['lng']
            else:
                error_count += 1
                continue
        except Exception as e:
            print(f"Geocoding error for {address}: {str(e)}")
            error_count += 1
            continue

        # Prepare record data
        record_data = {
            'clientId': uisp_record['clientId'],
            'firstName': uisp_record.get('firstName', ''),
            'lastName': uisp_record.get('lastName', ''),
            'isLead': not is_customer,
            'isCustomer': is_customer,
            'street1': uisp_record['street1'],
            'city': uisp_record['city'],
            'lat': lat,
            'lng': lng
        }

        try:
            # Try to get existing record
            customer = Customer.objects.get(clientId=uisp_record['clientId'])

            # Check if any fields have changed
            changed = False
            for key, value in record_data.items():
                if getattr(customer, key) != value:
                    changed = True
                    break

            # Update if changed
            if changed:
                for key, value in record_data.items():
                    setattr(customer, key, value)
                customer.save()
                updated_count += 1

        except Customer.DoesNotExist:
            # Create new record
            Customer.objects.create(**record_data)
            created_count += 1

    return {
        "message": f"Processed {len(uisp_data)} records",
        "stats": {
            "created": created_count,
            "updated": updated_count,
            "deleted": deleted_count,
            "errors": error_count
        }
    }
