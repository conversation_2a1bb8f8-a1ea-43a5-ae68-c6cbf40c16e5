html, body {
    height: 100%;
    margin: 0;
}

body {
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background-color: #f8f9fa;
    z-index: 1030;
    padding: 5px;
}

.content-wrapper {
    flex: 1;
    overflow-y: auto;
    margin-top: 60px;  /* Height of header */
    margin-bottom: 60px;  /* Height of footer */
    position: relative;
}

.footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    line-height: 60px;
    background-color: #f5f5f5;
    z-index: 1030;
}

/* For better display on mobile devices */
@media (max-width: 768px) {
    header {
        height: 50px;
    }

    .content-wrapper {
        margin-top: 50px;
        margin-bottom: 50px;
    }

    .footer {
        height: 50px;
        line-height: 50px;
    }
}
.mapholder{
    height: 90%;
    width: 100%;
}
#map {
    height: 100%;
  }