&nbsp;<a class="navbar-brand" href="#">ESVC</a>
<button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent"
        aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
    <span class="navbar-toggler-icon"></span>
</button>
<div class="collapse navbar-collapse" id="navbarSupportedContent">
    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
        <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown"
               aria-expanded="false">
                English Towers
            </a>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="{% url 'englishTowers:main' %}">Home</a></li>
                <li><a class="dropdown-item" href="{% url 'englishTowers:routers' %}">Routers</a></li>
                <li>
                    <hr class="dropdown-divider">
                </li>
                <li><a class="dropdown-item" href="#">New Order</a></li>
            </ul>
        </li>
        <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown"
               aria-expanded="false">
                SAM/UISP
            </a>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="{% url 'samUisp:main' %}">Network Status</a></li>
                <li><a class="dropdown-item" href="{% url 'samUisp:charts' %}">Utilization</a></li>
                <li>
                    <hr class="dropdown-divider">
                </li>
                <li><a class="dropdown-item" href="{% url 'samUisp:financials' %}">Financials</a></li>
                <li><a class="dropdown-item" href="{% url 'samUisp:customers' %}">Camden Customers</a></li>
            </ul>
        </li>
        <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown"
               aria-expanded="false">
                NC811
            </a>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="{% url 'NC811:tickets_summary' %}">NC811 Tickets</a></li>
            </ul>
        </li>
        <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown"
               aria-expanded="false">
                FiberPlan
            </a>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="{% url 'fiberPlan:vaults' %}">Vaults</a></li>
                <li><a class="dropdown-item" href="{% url 'fiberPlan:map' %}">Maps</a></li>
                <li>
                    <hr class="dropdown-divider">
                </li>
                <li><a class="dropdown-item" href="{% url 'fiberPlan:management' %}">Management</a></li>
                <li><a class="dropdown-item" href="{% url 'fiberPlan:upload_file' %}">Upload File</a></li>
            </ul>
        </li>
        <li class="nav-item">
            {% if request.user.is_superuser %}
                <a class="nav-link" href="{% url 'group_list' %}">Admin</a>
            {% else %}
                <a class="nav-link disabled" href="#" tabindex="-1" aria-disabled="true">Admin</a>
            {% endif %}
        </li>
        <li class="nav-item">
            <a class="nav-link disabled" aria-disabled="true">Disabled</a>
        </li>
    </ul>
    <div class="text-end">
        <a href="{% url 'profile_view' %}" class="btn btn-light btn-sm">
            <i class="fa-regular fa-user"></i>&nbsp;{{ user|upper }}
        </a>&nbsp;&nbsp;
        <form method="post" action="{% url 'logout' %}" class="d-inline">
            {% csrf_token %}
            <button type="submit" class="btn btn-danger btn-sm" style="width: 100px;">Logout</button>
        </form>&nbsp;&nbsp;
    </div>
</div>


{% for message in messages %}
    <div class="alert alert-dismissible {{ message.tags }}" role="alert">
        <div>{{ message | safe }}</div>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
{% endfor %}
<div id="liveAlertPlaceholder"></div>
