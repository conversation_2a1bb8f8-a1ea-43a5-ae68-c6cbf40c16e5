{% extends "bootstrap/base.html" %}
{% block content %}
<div class="container mt-5">
    <h2 class="mb-4">English Towers Transactional Dashboard</h2>
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card shadow-sm text-center">
                <div class="card-body">
                    <div class="fw-bold fs-4">${{ total_sales|floatformat:2 }}</div>
                    <div class="text-muted">Total Sales (MTD)</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card shadow-sm text-center">
                <div class="card-body">
                    <div class="fw-bold fs-4">{{ num_orders }}</div>
                    <div class="text-muted">Orders (MTD)</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card shadow-sm text-center">
                <div class="card-body">
                    <div class="fw-bold fs-4">{{ num_active }}</div>
                    <div class="text-muted">Active Rentals</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card shadow-sm text-center">
                <div class="card-body">
                    <div class="fw-bold fs-4">${{ avg_order_value|floatformat:2 }}</div>
                    <div class="text-muted">Avg. Order Value</div>
                </div>
            </div>
        </div>
    </div>
    <h4 class="mt-5">Recent Orders</h4>
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Date</th>
                <th>Unit</th>
                <th>Email</th>
                <th>Price</th>
                <th>Status</th>
            </tr>
        </thead>
        <tbody>
        {% for order in recent_orders %}
            <tr>
                <td>{{ order.created_at|date:"M d, Y H:i" }}</td>
                <td>{{ order.unit }}</td>
                <td>{{ order.email }}</td>
                <td>${{ order.price|floatformat:2 }}</td>
                <td>{{ order.approval }}</td>
            </tr>
        {% empty %}
            <tr><td colspan="5" class="text-center text-muted">No recent orders</td></tr>
        {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}
