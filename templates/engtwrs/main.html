{% extends "bootstrap/base.html" %}
{% block content %}
    <div class="row mx-2">
        <div class="col">
            <h1 class="display-6">English Towers</h1>
            {% if settings.STRIPE_MODE == "DEMO" %}
                <small class="text-warning">
                    <i class="fas fa-info-circle"></i> Test Environment
                </small>
            {% else %}
                <small class="text-success">
                    <i class="fas fa-check-circle"></i> Production
                </small>
            {% endif %}
        </div>
    </div>
    <div class="row mx-2">
        <div class="col">
            <div class="row">
                <p>Active Networks:</p>
                <table id="activetable" class="table table-striped table-hover compact" style="width: 100%">
                    <thead>
                    <tr>
                        <th>Unit</th>
                        <th>Password</th>
                        <th>Speed</th>
                        <th>Plan Days</th>
                        <th>Days Left</th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="row mt-4">
                <h3>New Rental Network</h3>
                <div class="col">
                    <form class="needs-validation" method="post" action="{% url 'englishTowers:newrental' %}"
                          novalidate id="rentalForm">
                        {% csrf_token %}
                        <div class="input-group mb-3">
                            <span class="input-group-text" id="inputGroup-sizing-default">Unit Number</span>
                            <input type="text" id="unit" name="unit" class="form-control"
                                   aria-label="Sizing example input" required pattern="[0-9]{3,4}"
                                   aria-describedby="inputGroup-sizing-default">
                            <div id="profileExistsAlert" class="invalid-feedback" style="display: none;"></div>
                        </div>
                        <div class="input-group mb-3">
                            <span class="input-group-text" id="inputGroup-sizing-default">Password</span>
                            <input type="text" id="password" name="password" class="form-control"
                                   aria-label="Sizing example input" required
                                   pattern="^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,}$"
                                   aria-describedby="inputGroup-sizing-default" disabled>
                            <button type="button"
                                    class="btn btn-outline-secondary btn-sm"
                                    id="togglePassword"
                                    style="font-size: 0.8rem;"
                                    aria-label="Toggle password visibility">
                                Specify Pass
                            </button>
                            <div class="invalid-feedback">Enter Password (8 characters)</div>
                        </div>
                        <div class="input-group mb-3">
                            <span class="input-group-text" id="inputGroup-sizing-default">Email</span>
                            <input type="email" id="email" name="email" class="form-control"
                                   aria-label="Email input" required
                                   pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
                                   aria-describedby="inputGroup-sizing-default">
                            <div class="invalid-feedback">Enter a valid email address</div>
                        </div>
                        <div class="input-group mb-3">
                            <span class="input-group-text" id="inputGroup-sizing-default">Number of Days</span>
                            <input type="text" id="days" name="days" class="form-control"
                                   aria-label="Sizing example input" required pattern="[0-9]{1,3}"
                                   aria-describedby="inputGroup-sizing-default">
                            <div class="invalid-feedback">Enter Number of Days</div>
                        </div>
                        <select class="form-select mb-3" id="package" name="package" aria-label="Default select example"
                                required>
                            <option selected disabled value="">Select Package</option>
                            <option value="standard">Standard</option>
                            <option value="streaming">Streaming</option>
                        </select>
                        <div class="invalid-feedback">Select Internet Package</div>
                        <input type="hidden" id="input-price" name="price" value="0">
                        <input type="hidden" id="input-expires" name="expires" value="0">
                        <input type="hidden" id="hidden_hard_expiration_date" name="hard_expiration_date" value="">
                        <div class="d-flex align-items-center">
                            <button type="submit" class="btn btn-primary">Submit</button>
                            <div class="form-check ms-3">
                                <input class="form-check-input" type="checkbox" id="createNetwork" name="network_only"
                                       checked>
                                <label class="form-check-label fw-bold text-primary" for="createNetwork"
                                       style="font-size: 1.1em;">
                                    Create Network
                                </label>
                            </div>
                            <div class="form-check ms-3">
                                <input class="form-check-input" type="checkbox" id="processCC" name="process_cc"
                                       checked>
                                <label class="form-check-label fw-bold text-primary" for="processCC"
                                       style="font-size: 1.1em;">
                                    Process CC       <br> <select id="paymentMethod" name="paymentMethod" class="ms-2 text-dark"
                                        style="font-size: 0.65em; width: auto;">
                                    <option value="email" selected>Email Paylink</option>
                                    <option value="helpdesk">Helpdesk Checkout</option>
                                </select>
                                </label>

                            </div>

                        </div>
                    </form>
                </div>
                <div class="col">
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" id="unit_number" placeholder="Lookup Unit Number"
                               aria-label="unit" aria-describedby="basic-addon2">
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary" id='search_unit' type="button">Search</button>
                        </div>
                    </div>
                    <table class="table table-striped table-hover compact" style="width:100%">
                        <tr>
                            <td>Rental Expires:</td>
                            <td><span class='expires'></span></td>
                        </tr>
                        <tr>
                            <td>Price Estimate:</td>
                            <td><span class='estprice'></span></td>
                        </tr>
                        <tr>
                            <td>Hard Expiration Date:</td>
                            <td><input type="date" id="hard_expiration_date" name="hard_expiration_date" class="form-control"></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="row">
                <p>Orders placed this month:</p>
                <table id="ordertable" class="table table-striped table-hover compact" style="width: 100%">
                    <thead>
                    <tr>
                        <th>Unit</th>
                        <th>Price</th>
                        <th>Transaction ID</th>
                        <th>Approval</th>
                        <th>Order Date</th>
                    </tr>
                    </thead>
                </table>
            </div>
            <div class="row mt-4">

                <h3>Recent Order Info</h3>
                <table id="recent_order_info_table" class="table table-striped table-hover compact"
                       style="width:100%">
                    <thead>
                    <tr>
                        <th>Customer Email</th>
                        <th>Status</th>
                        <th>Order Summary</th>
                    </tr>
                    </thead>
                    <tbody></tbody>
                </table>

            </div>
        </div>
    </div>

    <div class="modal fade" id="confirmationModal" tabindex="-1" aria-labelledby="confirmationModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmationModalLabel">Confirm Rental Network Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="confirmationModalBody">
                    </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">No</button>
                    <button type="button" class="btn btn-primary" id="confirmSubmitBtn">Yes</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        (function () {
            'use strict'
            // Fetch all the forms we want to apply custom Bootstrap validation styles to
            let forms = document.querySelectorAll('.needs-validation')
            // Loop over them and prevent submission
            Array.prototype.slice.call(forms)
                .forEach(function (form) {
                    form.addEventListener('submit', function (event) {
                        if (!form.checkValidity()) {
                            event.preventDefault()
                            event.stopPropagation()
                        } else {
                            // If form is valid, prevent default submission and show confirmation modal
                            event.preventDefault();
                            setupConfirmationModal();
                            $('#confirmationModal').modal('show');
                        }
                        form.classList.add('was-validated')
                    }, false)
                })
        })()

        function calcPrice(selected_package, days) {
            let price = 0;
            if (selected_package == 'standard') {
                switch (parseInt(days)) {
                    case 3:
                        price = '24.95';
                        break;
                    case 4:
                        price = '29.95';
                        break;
                    case 5:
                        price = '35.95';
                        break;
                    case 6:
                        price = '42.95';
                        break;
                    case 7:
                        price = '49.95';
                        break;
                    default:
                        price = (((days - 7) * 7) + 49.95).toFixed(2);
                        break;
                }
            }
            if (selected_package == 'streaming') {
                switch (parseInt(days)) {
                    case 3:
                        price = '34.95';
                        break;
                    case 4:
                        price = '39.95';
                        break;
                    case 5:
                        price = '45.95';
                        break;
                    case 6:
                        price = '52.95';
                        break;
                    case 7:
                        price = '59.95';
                        break;
                    default:
                        price = (((days - 7) * 7) + 59.95).toFixed(2);
                        break;
                }
            }
            return price
        }

        function setupConfirmationModal() {
            const unit = $('#unit').val();
            const email = $('#email').val() || "no email provided";
            const days = $('#days').val();
            const expirationDate = $('#hard_expiration_date').val();
            const packageName = $('#package option:selected').text();
            const processCC = $('#processCC').prop('checked') ? true : false;
            const createNetwork = $('#createNetwork').prop('checked');
            const paymentMethodIsEmail = $('#paymentMethod').val() === 'email';
            const price = $('#input-price').val();
            
            let paymentMethod = "";
            if (processCC) {
                if (paymentMethodIsEmail) {
                    paymentMethod = "The payment link will be emailed to the customer.";
                } else {
                    paymentMethod = "The transaction/credit information will be completed on the next page.";
                }
            }

            let networkCreatedWhen = "";
            if (!processCC && createNetwork) { // Corrected: processCC is "No" (meaning checkbox is unchecked) and createNetwork is checked
                networkCreatedWhen = "Immediately";
            } else {
                networkCreatedWhen = "The network will be automatically created after successful payment is received, network details and receipt will be emailed directly to the customer";
            }


            const summaryHtml = `
                <p><strong>Rental Details:</strong> <b>Unit: </b>${unit}, <b>Days:</b> ${days}, <b>Package:</b> ${packageName}</p>
                <p><strong>Email:</strong> ${email}</p>
                <p><strong>Expiration Date:</strong> ${expirationDate}</p>
                <p><strong>Price:</strong> $${price}</p>
                <p><strong>Process CC?:</strong> ${processCC ? "Credit Card will be processed." : "Credit Card will not be processed."}</p> 
                ${processCC === true ? `<p><strong>Payment Method:</strong> ${paymentMethod}</p>` : ''}
                <p><strong>When is network created:</strong> ${networkCreatedWhen}</p>
            `;
            $('#confirmationModalBody').html(summaryHtml);
        }

        $(document).ready(function () {
            // Example starter JavaScript for disabling form submissions if there are invalid fields
            // https://getbootstrap.com/docs/5.0/forms/validation/

            $('#days').each(function () {
                var elem = $(this);
                elem.data('oldVal', elem.val());
                elem.on('change', function (event) {
                    if (elem.data('oldVal') != elem.val()) {
                        elem.data('oldVal', elem.val());
                        const targetDate = new Date();
                        targetDate.setDate(targetDate.getDate() + Number(elem.val()));
                        $('.expires').text('Noon on ' + targetDate.toDateString());
                        $('#input-expires').val('Noon on ' + targetDate.toDateString());

                        // Set both the visible and hidden date fields
                        const dateStr = targetDate.toISOString().split('T')[0];
                        $('#hard_expiration_date').val(dateStr);
                        $('#hidden_hard_expiration_date').val(dateStr);

                        var selected_package = $('#package').val();
                        var targetPrice = 't.b.d.'
                        if (selected_package == 'streaming' || selected_package == 'standard') {
                            targetPrice = calcPrice(selected_package, elem.val());
                        }
                        $('.estprice').text(targetPrice);
                        $('#input-price').val(targetPrice);
                    }
                }).trigger('change');
            });

            // Add this to handle manual changes to the date input
            $('#hard_expiration_date').on('change', function() {
                $('#hidden_hard_expiration_date').val($(this).val());
            });

            $('#package').each(function () {
                let elem = $(this);
                elem.data('oldVal', elem.val());
                elem.on('change', function (event) {
                    if (elem.data('oldVal') != elem.val()) {
                        elem.data('oldVal', elem.val());
                        var days = $('#days').val();
                        var targetPrice = 't.b.d.'
                        if (days > 0) {
                            targetPrice = calcPrice(elem.val(), days);
                        }
                        $('.estprice').text(targetPrice);
                        $('#input-price').val(targetPrice);
                    }
                }).trigger('change')
            })

            $("#search_unit").on('click', function (e) {
                e.preventDefault();
                let link = '/et/unit/' + $("#unit_number").val();
                window.location.href = link;
            })
            var table3 = $('#recent_order_info').DataTable({
                ajax: {
                    processing: true,
                    url: "{% url 'englishTowers:json' %}",
                    data: {
                        param: "recent_order_info"
                    },
                    dataSrc: "",
                },
                scrollY: "250px", // Enable vertical scrolling with a smaller height
                scrollCollapse: true,
                pageLength: 4, // Show 4 rows max
                lengthMenu: [4, 5], // Allow scrolling after 5 rowsd
                paging: false,
                layout: {
                    topEnd: null,
                },
                columnDefs: [
                    {
                        targets: -1,
                        className: 'dt-body-right'
                    }
                ],
                columns: [
                    {"data": "email"},
                    {"data": "order_summary"}
                ],
            });

            var table = $('#ordertable').DataTable({
                ajax: {
                    processing: true,
                    url: "{% url 'englishTowers:json' %}",
                    data: {
                        param: "orders"
                    },
                    dataSrc: "",
                },
                scrollY: "300px", // Enable vertical scrolling
                scrollCollapse: true,
                paging: true,
                pageLength: 6,// Show only 6 rows; scrolling after th
                layout: {
                    //topEnd: null,
                },
                columnDefs: [
                    {
                        targets: -1,
                        className: 'dt-body-left'
                    }
                ],
                columns: [
                    {
                        data: "unit"
                    },
                    {data: "price"},
                    {data: "transactionID"},
                    {data: "approval"},
                    {
                        data: "created_at",
                        render: function (data, type, row) {
                            var dateSplit = data.split('-');
                            var monthSplit = dateSplit[2].split('T')
                            return type === "display" || type === "filter" ?
                                dateSplit[1] + '-' + monthSplit[0] + '-' + dateSplit[0] :
                                data;
                        }
                    },
                ],
            });
            var table2 = $('#activetable').DataTable({
                ajax: {
                    // Show only 6 rows; scrolling after that
                    processing: true,
                    url: "{% url 'englishTowers:json' %}",
                    data: {
                        param: "active"
                    },
                    dataSrc: "",
                },
                scrollY: "300px", // Enable vertical scrolling
                scrollCollapse: true,
                paging: true, // Disable paging
                pageLength: 6,
                layout: {
                    topEnd: null,
                },
                columnDefs: [
                    {
                        targets: -1,
                        className: 'dt-body-right'
                    }
                ],
                columns: [
                    {data: "shortname"},
                    {data: "passwd"},
                    {data: "speed"},
                    {data: "days"},
                    {data: "daysLeft"},
                ],
            });
            var table3 = $('#recent_order_info_table').DataTable({
                ajax: {
                    processing: true,
                    url: "{% url 'englishTowers:json' %}",
                    data: {
                        param: "recent_order_info"
                    },
                    dataSrc: "",
                },
                scrollY: "250px", // Enable vertical scrolling with a smaller height
                scrollCollapse: true,
                pageLength: 4, // Show 4 rows max
                lengthMenu: [4, 5], // Allow scrolling after 5 rows
                paging: false,
                layout: {
                    topEnd: null,
                },
                columnDefs: [
                    {
                        targets: 1,
                        className: 'dt-left'
                    }
                ],
                columns: [
                    {data: "email"},
                    {data: "status"},
                    {data: "order_summary"}
                ],
            });

            // Add a timer variable outside the event handler
            let typingTimer;
            const doneTypingInterval = 1000; // 1 second delay

            $('#unit').on('input', function() {
                // Clear the timer on every keystroke
                clearTimeout(typingTimer);

                // Enable the checkbox when unit changes
                $('#createNetwork').prop('disabled', false);

                // Clear the alert message and restore checkbox immediately when user starts typing
                $('#profileExistsAlert')
                    .removeClass('d-block alert alert-danger')
                    .css('display', 'none')
                    .html('');

                $('#createNetwork')
                    .prop('disabled', false)
                    .prop('checked', true);

                // Clear and unlock form fields when unit changes
                $('#email').val('')
                    .attr('readonly', false)
                    .css('background-color', '');
                $('#days').val('')
                    .attr('readonly', false)
                    .css('background-color', '');
                $('#package').val('')
                    .attr('readonly', false)
                    .css('background-color', '');
                $('#hard_expiration_date').val('')
                    .attr('readonly', false)
                    .css('background-color', '');
                $('#hidden_hard_expiration_date').val('');

                // Set a new timer
                typingTimer = setTimeout(function() {
                    // Get the trimmed value from the unit input
                    const unitValue = $('#unit').val().trim();

                    if (unitValue) {
                        $.ajax({
                            url: "{% url 'englishTowers:json' %}",
                            type: 'GET',
                            data: {
                                param: 'profile_exists',
                                unit: unitValue
                            },
                            success: function(response) {
                                if (response.exists) {
                                    // Profile exists - uncheck and disable the createNetwork checkbox
                                    $('#createNetwork')
                                        .prop('checked', false)
                                        .prop('disabled', true);

                                    // Show striking message
                                    $('#profileExistsAlert')
                                        .html('<i class="fas fa-exclamation-triangle"></i> This network already exists! <small><a href="#" class="cleanap link-primary">Click to clear network/order</a></small>')
                                        .addClass('d-block alert alert-danger')
                                        .css({
                                            'display': 'block',
                                            'font-weight': 'bold',
                                            'font-size': '1.1em',
                                            'margin-top': '10px',
                                            'width': '100%'
                                        });

                                    // If rental_order_info exists and has data, populate and lock form fields
                                    if (response.rental_order_info && response.rental_order_info.length > 0) {
                                        const orderInfo = response.rental_order_info[0];

                                        // Handle isPaid status for processCC checkbox
                                        if (orderInfo.isPaid === 1) {
                                            $('#processCC')
                                                .prop('checked', false)
                                                .prop('disabled', true);
                                        } else {
                                            // Allow processCC to be checked even when createNetwork is disabled
                                            // but only in this autofill scenario
                                            $('#processCC').prop('disabled', false);
                                        }

                                        // Populate form fields with data from rental_order_info, checking for null values
                                        if (orderInfo.email) {
                                            $('#email').val(orderInfo.email)
                                                .attr('readonly', true)
                                                .css('background-color', '#e9ecef');
                                        }

                                        if (orderInfo.et_days) {
                                            $('#days').val(orderInfo.et_days)
                                                .attr('readonly', true)
                                                .css('background-color', '#e9ecef');
                                            // Trigger change to update price calculation
                                            $('#days').trigger('change');
                                        }

                                        if (orderInfo.package) {
                                            $('#package').val(orderInfo.package)
                                                .attr('readonly', true)
                                                .css('background-color', '#e9ecef')
                                                .removeClass('is-invalid')
                                                .addClass('is-valid');
                                            // Trigger change to update price calculation
                                            $('#package').trigger('change');

                                            // Force validation to recognize the field as valid
                                            $('#package')[0].setCustomValidity('');
                                            $('#package')[0].reportValidity();
                                        }

                                        // Set the date fields if available
                                        if (orderInfo.hard_expiration_date) {
                                            $('#hard_expiration_date').val(orderInfo.hard_expiration_date)
                                                .attr('readonly', true)
                                                .css('background-color', '#e9ecef');
                                            $('#hidden_hard_expiration_date').val(orderInfo.hard_expiration_date);
                                        }
                                    }
                                }
                            }
                        });
                    } else {
                        // Clear everything if the input is empty
                        $('#profileExistsAlert')
                            .removeClass('d-block alert alert-danger')
                            .css('display', 'none')
                            .html('');

                        $('#createNetwork')
                            .prop('disabled', false)
                            .prop('checked', true);
                    }
                }, doneTypingInterval);
            });

            // Handle the "Yes" button click in the confirmation modal
            $('#confirmSubmitBtn').on('click', function() {
                // Remove the event listener to prevent re-triggering the modal
                $('#rentalForm').off('submit').submit();
            });
        });

        $(document).ready(function() {
            $('#togglePassword')
                .text('Specify Pass')
                .click(function() {
                    const passwordInput = $('#password');
                    const button = $(this);

                    if (button.text() === 'Specify Pass') {
                        // Enable password input
                        passwordInput.prop('disabled', false);
                        button.text('Auto-Generate');
                    } else {
                        // Disable and clear password input
                        passwordInput.prop('disabled', true);
                        passwordInput.val('');
                        button.text('Specify Pass');
                    }
                });

            $(document).on('click', '.cleanap', function(e) {
                e.preventDefault();
                const unitValue = $('#unit').val().trim();
                $.ajax({
                    url: "{% url 'englishTowers:json' %}",
                    type: 'GET',
                    data: {
                        param: 'reset_ap_by_unit',
                        unit: unitValue
                    },
                    dataType: 'json',
                    success: function(data){
                        // Trigger the input handler to refresh the UI state
                        $('#unit').trigger('input');
                    }
                });
            });

            // Add password validation
            $('#password').on('input', function() {
                const password = $(this).val();
                const hasLetter = /[A-Za-z]/.test(password);
                const hasNumber = /\d/.test(password);
                const isLongEnough = password.length >= 8;

                if (!password) {
                    $(this)[0].setCustomValidity('');
                    return;
                }

                if (!isLongEnough || !hasLetter || !hasNumber) {
                    let message = 'Password must:';
                    if (!isLongEnough) message += '\n- be at least 8 characters';
                    if (!hasLetter) message += '\n- contain at least one letter';
                    if (!hasNumber) message += '\n- contain at least one number';

                    $(this)[0].setCustomValidity(message);
                    $(this).next('.invalid-feedback').text(message.replace(/\n/g, ' '));
                } else {
                    $(this)[0].setCustomValidity('');
                }

                // Force validation UI update
                $(this)[0].reportValidity();
            });
        });

        $(document).ready(function () {
            // Function to check if at least one checkbox is checked and update submit button
            function updateSubmitButton() {
                const createNetworkChecked = $('#createNetwork').prop('checked');
                const processCCChecked = $('#processCC').prop('checked');
                $('button[type="submit"]').prop('disabled', !createNetworkChecked && !processCCChecked);
            }

            // Add event listeners to both checkboxes
            $('#createNetwork, #processCC').on('change', updateSubmitButton);

            // Initial check when page loads
            updateSubmitButton();
        });

        $(document).ready(function () {
            // Function to check if processCC is disabled and update select accordingly
            function updatePaymentMethodSelect() {
                $('#paymentMethod').prop('disabled', !$('#processCC').prop('checked'));
            }

            // Add event listener to processCC checkbox
            $('#processCC').on('change', updatePaymentMethodSelect);

            // Initial check when page loads
            updatePaymentMethodSelect();
        });

        $(document).ready(function() {
            // Existing updateSubmitButton function remains unchanged

            // Add new function to enforce checkbox dependency
            function enforceCheckboxDependency() {
                const createNetworkChecked = $('#createNetwork').prop('checked');
                const createNetworkDisabled = $('#createNetwork').prop('disabled');

                // Only enforce dependency if we're not in the autofill scenario
                // (when createNetwork is disabled due to existing network)
                if (!createNetworkDisabled && !createNetworkChecked) {
                    $('#processCC').prop('checked', false);
                }
            }

            // Add event listener to createNetwork checkbox
            $('#createNetwork').on('change', enforceCheckboxDependency);

            // Prevent processCC from being checked if createNetwork is unchecked
            // (except in the autofill scenario)
            $('#processCC').on('click', function(e) {
                const createNetworkChecked = $('#createNetwork').prop('checked');
                const createNetworkDisabled = $('#createNetwork').prop('disabled');

                if (!createNetworkDisabled && !createNetworkChecked && !$(this).prop('checked')) {
                    e.preventDefault();
                    return false;
                }
            });
        });

        $(document).ready(function() {
            // Function to check if both checkboxes are disabled and update submit button
            function updateSubmitButtonDisabledState() {
                const createNetworkDisabled = $('#createNetwork').prop('disabled');
                const processCCDisabled = $('#processCC').prop('disabled');
                
                $('button[type="submit"]').prop('disabled', createNetworkDisabled && processCCDisabled);
            }
            
            // Add event listeners to detect when checkboxes become disabled
            $('#createNetwork, #processCC').on('disabled', updateSubmitButtonDisabledState);
            
            // Run initial check
            updateSubmitButtonDisabledState();
            
            // Use MutationObserver to detect attribute changes (disabled state)
            const observer = new MutationObserver(updateSubmitButtonDisabledState);
            
            // Observe both checkboxes for attribute changes
            observer.observe($('#createNetwork')[0], { attributes: true });
            observer.observe($('#processCC')[0], { attributes: true });
        });
    </script>
    <style>
        #paymentMethod:disabled {
            background-color: #e9ecef;
            opacity: 0.65;
            cursor: not-allowed;
        }
    </style>
{% endblock %}