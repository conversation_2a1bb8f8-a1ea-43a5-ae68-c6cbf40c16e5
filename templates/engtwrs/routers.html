{%  extends "bootstrap/base.html" %}
{% block content %}
    <div class="row mx-2">
        <div class="col"><h1 class="display-6">English Towers</h1></div>
    </div>
    <div class="row mx-2">
        <div class="col">    
            <table id="routertable" class="table table-striped table-hover compact" style="width: 100%">
                <thead>
                <tr>                    
                    <th>Hostname</th>
                    <th>Router Model</th>
                    <th>Firmware</th>
                    <th>Rental Network</th>
                    <th>Queue name</th>
                    <th>IP Address</th>                    
                </tr>
                </thead>
            </table>
        </div>
    </div>
<script>
    $(document).ready(function() {
        var table = $('#routertable').DataTable({
           ajax: {
               processing: true,
               url: "{% url 'englishTowers:json' %}",
               data: {
                  param: "get_routers"
               },
               dataSrc: "",
            },
            columns: [
                    { data: "hostname",
                       render: function (data) {
                            return "<a href='unit/" + data + "' class='link-primary'>" + data + "</a>"
                    }},
                    { data: "model"},
                    { data: "firmware"},
                    { data: "rental",
                        render: function(data){
                            if(data==1) {
                                return "Yes"
                            }
                            return "No"
                            }},
                    { data: "queue_name"},
                    { data: "ipaddress"},
                ],
        });
    });
</script>
{% endblock %}