{% extends "bootstrap/base.html" %}
{% block content %}
    <div class="row mx-2">
        <div class="col"><h1 class="display-6"><a href="{% url 'englishTowers:main' %}"><i class="fa-solid fa-house"></i></a>&nbsp;&nbsp;English Towers</h1><h3>Unit: {{ id }} IP: {{ ipaddress }}</h3>
        Available Actions: <a href="#" class="cleanap link-primary">Clean AP</a> | <a href="#" class="updatequeue link-primary">Update Queue</a></div>
    </div>
    <div class="row mx-2">
        <div class="col">
            <p>Order to date:</p>
            <table id="ordertable" class="table table-striped table-hover compact" style="width: 100%">
            <thead>
            <tr>                    
                <th>Transaction ID</th>
                <th>Price</th>
                <th>Date</th>
            </tr>
            </thead>
            </table>
        </div>
        <div class="col-7">
            <p>Router Configuration:</p>
            <table id="ssidtable" class="table table-striped table-hover compact" style="width: 100%">
            <thead>
            <tr>                    
                <th>SSID</th>
                <th>Profile</th>
                <th>Password</th>
                <th>Int</th>
                <th>Band</th>
                <th>Freq</th>
            </tr>
            </thead>
            </table>
            <p>Wireless Clients:</p>
            <table id="registrationtable" class="table table-striped table-hover compact" style="width: 100%">
            <thead>
            <tr>
                <th>Interface</th>
                <th>Mac Addr</th>
                <th>Signal</th>
                <th>TX</th>
                <th>RX</th>
                <th>Uptime</th>
            </tr>
            </thead>
            </table>
            <p>Queues:</p>
            <table id="mytable" class="table table-striped table-hover compact" style="width: 100%">
            <thead>
            <tr>
                <th>Name</th>
                <th>Target</th>
                <th>Limit</th>
                <th>Threshold</th>
                <th>Burst</th>
            </tr>
            </thead>
            </table>
        </div>
    </div>      
<script>
    $(document).ready(function() {
        
        $(".cleanap").click(function(e) {
            e.preventDefault();
            $.ajax({
                url: "{% url 'englishTowers:json' %}",
                type: 'GET',
                data: {
                    param: 'reset_ap',
                    ipaddress: '{{ ipaddress }}',
                },
                dataType: 'json',
                success: function(data){
                    ssidtable.ajax.reload();
                }
            });
        });
        
        $(".updatequeue").click(function(e) {
            e.preventDefault();
            $.ajax({
                url: "{% url 'englishTowers:json' %}",
                type: 'GET',
                data: {
                    param: 'update_queue',
                    ipaddress: '{{ ipaddress }}',
                },
                dataType: 'json',
                success: function(data){
                    queuetable.ajax.reload();
                }
            });
        });
        
        var ordertable = $('#ordertable').DataTable({
           ajax: {
              processing: true,
              url: "{% url 'englishTowers:json' %}", 
                data: {
                  param: "unit_order",
                  unit : "{{ id }}",
               },
              dataSrc: "",
            },
            columns: [
                    { data: "transactionID"},
                    { data: "price"},
                    { data: "created_at",
                      render: function(data, type, row) {
                        var dateSplit = data.split('-');
                        var monthSplit = dateSplit[2].split('T')
                        return type === "display" || type === "filter" ?
                            dateSplit[1] + '-' + monthSplit[0] + '-' + dateSplit[0] :
                            data;
                      }},
                ],
        });
        var ssidtable = $('#ssidtable').DataTable({
           ajax: {
              processing: true,
              url: "{% url 'englishTowers:json' %}",
               data: {
                  param: "router_ssid",
                  ipaddress : "{{ ipaddress }}",
               },
              dataSrc: "",
            },
            columns: [
                    { data: "ssid"},
                    { data: "security-profile"},
                    { data: "password"},
                    { data: "master-interface"},
                    { data: "band"},
                    { data: "frequency"},
                ],
        });
        var regtable = $('#registrationtable').DataTable({
           ajax: {
              processing: true,
              url: "{% url 'englishTowers:json' %}",
               data: {
                  param: "router_registrations",
                  ipaddress : "{{ ipaddress }}",
               },
              dataSrc: "",
            },
            columns: [
                    { data: "interface"},
                    { data: "mac-address"},
                    { data: "signal-strength"},
                    { data: "tx-rate"},
                    { data: "rx-rate"},
                    { data: "uptime"},
                ],
        });
        var queuetable = $('#mytable').DataTable({
           ajax: {
              processing: true,
              url: "{% url 'englishTowers:json' %}",
               data: {
                  param: "router_queues",
                  ipaddress : "{{ ipaddress }}",
               },
              dataSrc: "",
            },
            columns: [
                    { data: "name"},
                    { data: "target"},
                    { data: "max-limit",
                      render: function(data) {
                        var data_split = data.split('/');
                        var download = parseInt(data_split[0]/1000000) + 'M';
                        var upload = parseInt(data_split[1]/1000000) + 'M';
                        return download + '/' + upload;
                      }},
                    { data: "burst-threshold",
                      render: function(data) {
                          var data_split = data.split('/');
                          var download = parseInt(data_split[0] / 1000000) + 'M';
                          var upload = parseInt(data_split[1] / 1000000) + 'M';
                          return download + '/' + upload;
                      }},
                    { data: "burst-limit",
                      render: function(data) {
                          var data_split = data.split('/');
                          var download = parseInt(data_split[0] / 1000000) + 'M';
                          var upload = parseInt(data_split[1] / 1000000) + 'M';
                          return download + '/' + upload;
                      }},
                ],
        });        
    });
</script>
{% endblock %}