<!-- templates/fiberPlan/signup.html -->
{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>ESVC Fiber Sign-Up</title>
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <link rel="stylesheet" href="{% static 'css/main.css' %}">
  <style>
    body { font-family: Arial, sans-serif; background: #f8fafc; margin: 0; }
    .signup-container { max-width: 740px; margin: 2rem auto; background: #fff; padding: 2rem 2.5rem; border-radius: 12px; box-shadow: 0 2px 16px #0002;}
    .section-title { font-size: 1.2rem; margin-bottom: 0.5rem; font-weight: bold;}
    .form-field { margin-bottom: 1rem;}
    label { display: block; margin-bottom: 0.35rem; font-weight: 500;}
    input, select, textarea { width: 100%; padding: 0.6em; border: 1px solid #bbb; border-radius: 6px; }
    .row { display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; }
    .legal { font-size: 0.95em; background: #f1f5f9; padding: 1em; border-radius: 8px; margin-bottom: 1em;}
    .signature-block { margin-top: 1.5em;}
    .submit-btn { width: 100%; background: #134e4a; color: #fff; padding: 0.85em; font-size: 1.15em; border-radius: 7px; border: none; font-weight: bold; cursor: pointer;}
    .submit-btn:disabled { background: #ccc;}
    .logo { width: 140px; margin-bottom: 1rem;}
    .bg-light { background: #f1f5f9; }
    .inline { display: flex; align-items: center; gap: .5rem; margin-bottom: 0.5rem; }
    .inline input[type="checkbox"] { margin: 0; width: auto; }
    input[readonly] { background: #f8fafc; color: #374151; font-weight: 600; }
    .money-input-wrapper { position: relative; }
    .money-input-wrapper::before { content: '$'; position: absolute; left: 0.6em; top: 50%; transform: translateY(-50%); color: #374151; font-weight: 500; pointer-events: none; z-index: 1; }
    .money-input-wrapper input { padding-left: 1.5em; }
  </style>
</head>
<body>
  <div class="signup-container">
    <h2>Fiber Internet Sign-Up Form</h2>
    <p>Thank you for choosing Eastern Shore Communications. Please complete your information below to finalize your service agreement.</p>

    <form method="post" autocomplete="off">
      {% csrf_token %}
      <input type="hidden" name="token" value="{{ token }}">

      <div class="section-title">Contact Information</div>
      <div class="form-field">
        <label>Name</label>
        <input type="text" value="{{ customer.firstName }} {{ customer.lastName }}" disabled aria-disabled="true">
      </div>
      <div class="form-field">
        <label>Email</label>
        <input type="email" value="{{ customer.email }}" disabled aria-disabled="true">
      </div>

      <!-- Service Address -->
      <div class="section-title" style="margin-top:1rem;">Service Address</div>
      <div class="form-field">
        <label for="service_street">Address</label>
        <input id="service_street" name="service_street" type="text" required
               minlength="5" maxlength="100"
               pattern="^[a-zA-Z0-9\s\.,#-]+$"
               title="Please enter a valid street address (minimum 5 characters)"
               value="{{ prefill_service_street }}">
      </div>
      <div class="row">
        <div class="form-field">
          <label for="service_city">City</label>
          <input id="service_city" name="service_city" type="text" required
                 minlength="2" maxlength="50"
                 pattern="^[a-zA-Z\s\-\.]+$"
                 title="Please enter a valid city name (letters, spaces, hyphens, and periods only)"
                 value="{{ prefill_service_city }}">
        </div>
        <div class="form-field">
          <label for="service_state">State</label>
          <input id="service_state" name="service_state" type="text"
                 minlength="2" maxlength="2" required
                 pattern="^[A-Z]{2}$"
                 title="Please enter a valid 2-letter state code (e.g., CA, NY, TX)"
                 style="text-transform: uppercase;"
                 value="{{ prefill_service_state }}">
        </div>
      </div>

      <!-- Billing Address -->
      <div class="section-title" style="margin-top:1rem;">
        Billing Address
        <label style="float:right; font-weight:normal;">
          <input id="same_as_service" type="checkbox" checked> Same As Service Address
        </label>
      </div>
      <div class="form-field">
        <label for="billing_street">Address</label>
        <input id="billing_street" name="billing_street" type="text" required
               minlength="5" maxlength="100"
               pattern="^[a-zA-Z0-9\s\.,#-]+$"
               title="Please enter a valid street address (minimum 5 characters)"
               value="{{ prefill_billing_street }}">
      </div>
      <div class="row">
        <div class="form-field">
          <label for="billing_city">City</label>
          <input id="billing_city" name="billing_city" type="text" required
                 minlength="2" maxlength="50"
                 pattern="^[a-zA-Z\s\-\.]+$"
                 title="Please enter a valid city name (letters, spaces, hyphens, and periods only)"
                 value="{{ prefill_billing_city }}">
        </div>
        <div class="form-field">
          <label for="billing_state">State</label>
          <input id="billing_state" name="billing_state" type="text"
                 minlength="2" maxlength="2" required
                 pattern="^[A-Z]{2}$"
                 title="Please enter a valid 2-letter state code (e.g., CA, NY, TX)"
                 style="text-transform: uppercase;"
                 value="{{ prefill_billing_state }}">
        </div>
      </div>

      <div class="section-title">Select Your Plan</div>
      <div class="row">
        <div class="form-field">
          <label for="plan_selected">Plan</label>
          <select id="plan_selected" name="plan_selected" required>
            <option value="">-- Choose Plan --</option>
            <option value="Bronze" data-price="25.00">Bronze: $25/mo (25 Mbps/10 Mbps)</option>
            <option value="Silver" data-price="60.00">Silver: $60/mo (250 Mbps/10 Mbps) - Most Popular</option>
            <option value="Gold" data-price="90.00">Gold: $90/mo (500 Mbps/20 Mbps)</option>
          </select>
        </div>
        <div class="form-field">
          <label for="plan_price">Monthly Price</label>
          <input id="plan_price" name="plan_price" type="text" value="$0.00" readonly>
        </div>
      </div>

      <div class="section-title">Select Your Protection Plan</div>
      <div class="row">
        <div class="form-field">
          <label for="protection_plan">Protection Plan</label>
          <select id="protection_plan" name="protection_plan">
            <option value="">-- Choose Plan --</option>
            <option value="Individual Bundle" data-price="14.95">
              Individual Bundle: Protect your phone &amp; electronics - $14.95
            </option>
            <option value="Home Plan" data-price="24.95">
              Home Plan: Protect your home electronics - $24.95
            </option>
            <option value="Family Bundle" data-price="44.95">
              Family Bundle: Protect your home, phone &amp; electronics - $44.95
            </option>
          </select>
        </div>
        <div class="form-field">
          <label for="protection_price">Monthly Price</label>
          <input id="protection_price" name="protection_price" type="text" value="$0.00" readonly>
        </div>
      </div>

      <!-- Fees -->
      <div class="row">
        <div class="form-field">
          <label for="install_fee">Internet Installation and Setup Fee</label>
          <input id="install_fee" name="install_fee" type="number" step="0.01" min="0" value="">
        </div>
        <div class="form-field">
          <label for="network_maintenance_fee">Network Maintenance Fee</label>
          <div class="money-input-wrapper">
            <input id="network_maintenance_fee" name="network_maintenance_fee" type="number" step="0.01" min="0" value="7.95">
          </div>
        </div>
      </div>

      <!-- Equipment choices + driven fees -->
      <div class="section-title">Equipment</div>
      <div class="form-field">
        <label class="inline">
          <input type="checkbox" id="cb_modem" name="equipment_modem"> Fiber Modem (Lease $4.95/mo)
        </label>
        <label class="inline">
          <input type="checkbox" id="cb_combo" name="equipment_combo"> Fiber Modem and Router Combo (Lease $15.00/mo)
        </label>
      </div>

      <div class="row">
        <div class="form-field">
          <label for="modem_lease_fee">Modem Lease Fee</label>
          <input id="modem_lease_fee" name="modem_lease_fee" type="number" step="0.01" min="0" value="">
        </div>
        <div class="form-field">
          <label for="router_modem_combo_lease_fee">Router and Modem Combo Lease Fee </label>
          <input id="router_modem_combo_lease_fee" name="router_modem_combo_lease_fee" type="number" step="0.01" min="0" value="">
        </div>
      </div>

      <div class="form-field">
        <label for="additional_construction_costs">Additional Construction Costs (if any)</label>
        <input id="additional_construction_costs" name="additional_construction_costs" type="number" step="0.01" min="0">
      </div>

      <!-- Total Amount -->
      <div class="form-field" style="margin-top: 1.5rem; padding: 1rem; background: #f1f5f9; border-radius: 8px;">
        <label for="total_amount" style="font-size: 1.1rem; font-weight: bold;">Total Amount</label>
        <input id="total_amount" name="total_amount" type="text" value="$0.00" readonly style="font-size: 1.1rem; font-weight: bold; background: #fff;">
      </div>


      <div class="legal">
        <strong>Terms &amp; Conditions:</strong>
        <ul>
          <li>By signing, you agree to the <a href="https://www.esvc.us/terms" target="_blank" rel="noopener">Terms and Conditions</a> and Acceptable Internet Use Policy of Eastern Shore Communications, LLC.</li>
          <li>One-year service agreement. Early termination fees may apply.</li>
          <li>Customer Premise Equipment must be returned upon cancellation.</li>
          <li>Service subject to availability and pre-installation engineering.</li>
          <li>See <a href="https://www.esvc.us" target="_blank" rel="noopener">www.esvc.us</a> for further details.</li>
        </ul>
      </div>

      <div class="form-field">
        <input type="checkbox" name="agrees_to_terms" required id="agrees_to_terms">
        <label for="agrees_to_terms">I agree to the Terms &amp; Conditions and Acceptable Use Policy.</label>
      </div>

      <div class="form-field signature-block">
        <label for="signature">E-signature (type your full name)</label>
        <input type="text" name="signature" id="signature" required>
      </div>

      <button type="submit" class="submit-btn">Submit &amp; Sign</button>
    </form>
  </div>

  <script>
    (function () {
      // Total amount calculation logic - defined first so other functions can use it
      const totalAmountInput = document.getElementById('total_amount');

      // Array of field IDs to sum - easily configurable for adding new fields
      const moneyFieldIds = [
        'plan_price',
        'protection_price',
        'network_maintenance_fee',
        'modem_lease_fee',
        'router_modem_combo_lease_fee'
      ];

      function parseMoneyValue(value) {
        if (!value) return 0;
        // Handle both dollar format ($25.00) and number format (25.00)
        const cleanValue = value.toString().replace(/[$,]/g, '');
        const parsed = parseFloat(cleanValue);
        return isNaN(parsed) ? 0 : parsed;
      }

      function updateTotalAmount() {
        let total = 0;

        moneyFieldIds.forEach(fieldId => {
          const field = document.getElementById(fieldId);
          if (field) {
            total += parseMoneyValue(field.value);
          }
        });

        totalAmountInput.value = `$${total.toFixed(2)}`;
      }

      // Protection plan price updater
      const protectionSelect = document.getElementById('protection_plan');
      const protectionPriceInput = document.getElementById('protection_price');
      function updateProtectionPrice() {
        const opt = protectionSelect ? protectionSelect.options[protectionSelect.selectedIndex] : null;
        const price = (opt && opt.dataset && opt.dataset.price) ? Number(opt.dataset.price) : null;
        protectionPriceInput.value = price ? `$${price.toFixed(2)}` : '$0.00';
        updateTotalAmount(); // Update total when protection price changes
      }
      if (protectionSelect) {
        protectionSelect.addEventListener('change', updateProtectionPrice);
        updateProtectionPrice(); // init on load
      }

      // Same-as-service toggle logic
      const same = document.getElementById('same_as_service');
      const sStreet = document.getElementById('service_street');
      const sCity   = document.getElementById('service_city');
      const sState  = document.getElementById('service_state');
      const bStreet = document.getElementById('billing_street');
      const bCity   = document.getElementById('billing_city');
      const bState  = document.getElementById('billing_state');

      function upper2(el){ el.value = (el.value || '').toUpperCase().slice(0,2); }
      ['input','change','blur'].forEach(evt => {
        sState && sState.addEventListener(evt, () => upper2(sState));
        bState && bState.addEventListener(evt, () => upper2(bState));
      });

      function copyFromService() {
        bStreet.value = sStreet.value;
        bCity.value   = sCity.value;
        bState.value  = (sState.value || '').toUpperCase();
      }
      function setBillingDisabled(disabled) {
        [bStreet, bCity, bState].forEach(el => {
          el.readOnly = disabled;
          el.classList.toggle('bg-light', disabled);
        });
      }
      if (same && same.checked) { copyFromService(); setBillingDisabled(true); }
      if (same) {
        same.addEventListener('change', function () {
          if (same.checked) { copyFromService(); setBillingDisabled(true); }
          else { bStreet.value=''; bCity.value=''; bState.value=''; setBillingDisabled(false); bStreet.focus(); }
        });
      }
      [sStreet, sCity, sState].forEach(el => {
        el && el.addEventListener('input', () => { if (same && same.checked) copyFromService(); });
      });

      // Equipment fee toggle logic (mutually exclusive)
      const cbModem = document.getElementById('cb_modem');
      const cbCombo = document.getElementById('cb_combo');
      const modemFee  = document.getElementById('modem_lease_fee');
      const routerFee = document.getElementById('router_modem_combo_lease_fee');

      function setModemChecked(on) {
        cbModem.checked = on;
        if (on) {
          modemFee.value = '4.95';
          cbCombo.checked = false;
          routerFee.value = '';
        } else {
          modemFee.value = '';
        }
        updateTotalAmount(); // Update total when equipment fees change
      }
      function setComboChecked(on) {
        cbCombo.checked = on;
        if (on) {
          routerFee.value = '15.00';
          cbModem.checked = false;
          modemFee.value = '';
        } else {
          routerFee.value = '';
        }
        updateTotalAmount(); // Update total when equipment fees change
      }
      if (cbModem)  cbModem.addEventListener('change', () => setModemChecked(cbModem.checked));
      if (cbCombo)  cbCombo.addEventListener('change', () => setComboChecked(cbCombo.checked));
      // Start with neither selected
      setModemChecked(false);
      setComboChecked(false);

      // Plan price update logic
      const planSelect = document.getElementById('plan_selected');
      const planPriceInput = document.getElementById('plan_price');
      function updatePlanPrice() {
        const selectedOption = planSelect && planSelect.options[planSelect.selectedIndex];
        const price = (selectedOption && selectedOption.dataset.price) ? Number(selectedOption.dataset.price) : null;
        planPriceInput.value = price ? `$${price.toFixed(2)}` : '$0.00';
        updateTotalAmount(); // Update total when plan price changes
      }
      if (planSelect) {
        planSelect.addEventListener('change', updatePlanPrice);
        updatePlanPrice(); // init on load
      }

      // Add event listeners to all money fields to update total when they change
      moneyFieldIds.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
          field.addEventListener('input', updateTotalAmount);
          field.addEventListener('change', updateTotalAmount);
        }
      });

      // Initialize total amount on page load
      updateTotalAmount();
    })();
  </script>
</body>
</html>
