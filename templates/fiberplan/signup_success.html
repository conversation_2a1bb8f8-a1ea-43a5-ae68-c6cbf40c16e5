 {% extends "bootstrap/base.html" %}
{% load static %}

{% block content %}
<div class="container my-5">
  <!-- If your public signup page does NOT use the admin navbar, you can remove this include -->
  {% include "bootstrap/navi.html" %}

  <div class="row justify-content-center">
    <div class="col-lg-10">
      <div class="card shadow-sm">
        <div class="card-header d-flex align-items-center justify-content-between">
          <h1 class="h3 mb-0">Thank you — your signup is complete (this is a temporary page for testing, the real success page will be on ESVC.US</h1>


        <div class="card-body">
          <div class="alert alert-success mb-4" role="alert">
            <strong>Success!</strong> We’ve received your information and signature.
            {% if reference_id %}
              Reference ID: <span class="fw-semibold">{{ reference_id }}</span>
            {% endif %}
          </div>

          <p class="mb-4">
            You’ll receive a confirmation email shortly with a copy of your submission and next steps.
            Our team may reach out if we need anything else to schedule your installation.
          </p>

          <div class="row gy-3">
            <div class="col-md-6">
              <div class="border rounded p-3 h-100">
                <h2 class="h6 text-uppercase text-muted mb-3">Customer</h2>
                <dl class="row mb-0">
                  <dt class="col-5">Name</dt>
                  <dd class="col-7">
                    {% if customer %}
                      {{ customer.firstName }} {{ customer.lastName }}
                    {% else %}
                      —
                    {% endif %}
                  </dd>

                  <dt class="col-5">Email</dt>
                  <dd class="col-7">
                    {% if customer and customer.email %}
                      {{ customer.email }}
                    {% else %}
                      —
                    {% endif %}
                  </dd>

                  <dt class="col-5">Service Address</dt>
                  <dd class="col-7">
                    {% if customer and customer.street1 %}
                      {{ customer.street1 }}{% if customer.city %}, {{ customer.city }}{% endif %}
                    {% else %}
                      —
                    {% endif %}
                  </dd>
                </dl>
              </div>
            </div>

            <div class="col-md-6">
              <div class="border rounded p-3 h-100">
                <h2 class="h6 text-uppercase text-muted mb-3">Selected Plan</h2>
                <dl class="row mb-0">
                  <dt class="col-5">Plan</dt>
                  <dd class="col-7">
                    {% if signup and signup.plan_selected %}
                      {{ signup.plan_selected }}
                    {% else %}
                      —
                    {% endif %}
                  </dd>

                  <dt class="col-5">Install Fee</dt>
                  <dd class="col-7">
                    {% if signup and signup.install_fee %}${{ signup.install_fee }}{% else %}—{% endif %}
                  </dd>

                  <dt class="col-5">Preferred Install Date</dt>
                  <dd class="col-7">
                    {% if signup and signup.preferred_installation_date %}
                      {{ signup.preferred_installation_date }}
                    {% else %}
                      —
                    {% endif %}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <hr class="my-4" />

          <div class="d-flex flex-wrap gap-2">
            {# If you generate PDFs, wire these up to real URLs when ready #}
            {% if pdf_urls %}
              <a class="btn btn-outline-primary" href="{{ pdf_urls.signup }}" target="_blank" rel="noopener">
                View Signed Signup Form
              </a>
              {% if pdf_urls.contract %}
                <a class="btn btn-outline-primary" href="{{ pdf_urls.contract }}" target="_blank" rel="noopener">
                  View Service Contract
                </a>
              {% endif %}
              {% if pdf_urls.roe %}
                <a class="btn btn-outline-primary" href="{{ pdf_urls.roe }}" target="_blank" rel="noopener">
                  Right of Entry Agreement
                </a>
              {% endif %}
            {% endif %}

            <a class="btn btn-primary" href="/">Finish</a>
          </div>

          <p class="text-muted small mt-4 mb-0">
            Need help? Email <a href="mailto:<EMAIL>"><EMAIL></a> or call 252‑548‑6200.
          </p>
        </div>
      </div>

      <!-- Optional: Auto-redirect home after a few seconds -->
      {# <meta http-equiv="refresh" content="8; url=/" /> #}
    </div>
  </div>
</div>
{% endblock %}
