
{% extends 'bootstrap/base.html' %}
{% block content %}
{% load static %}
    <script>
    // Global variables
    let map;
    let vaultMarkersVisible = true;
    let fiberLinesVisible = true;
    let customersVisible = true;
    let vaultMarkers = [];
    let customerMarkers = [];
    let infoWindow;

    $(document).ready(function(){
        $.ajax({
          url: "{% url 'fiberPlan:mydata'%}",
          method: 'GET',
          success: function (data) {
              initMap(data);
          }
        });
    });

    async function initMap(data){
        const { Map, InfoWindow } = await google.maps.importLibrary("maps");
        const { AdvancedMarkerElement, PinElement } = await google.maps.importLibrary("marker");

        map = new Map(document.getElementById("map"), {
            center: { lat: 36.3515884, lng: -76.146703 },
            zoom: 12,
            mapTypeId: 'roadmap',
            mapId: "9e3cc3275c46848c",
        });

        try {
            $.getJSON("{% static 'geodata/camden_fiber.geojson' %}", function(data) {
                map.data.addGeoJson(data);
                map.data.setStyle({
                    strokeColor: 'red',
                    strokeWeight: 3
                });
                console.log('Loaded GeoJSON: ', data.features.length, ' features');
            }).fail(function(jqXHR, textStatus, errorThrown) {
                console.error("Failed to load GeoJSON:", textStatus, errorThrown);
            });
        } catch (e) {
            console.error("Error loading GeoJSON:", e);
        }

        infoWindow = new InfoWindow();

        // Create vault markers
        vaultMarkers = data['vaults']?.map((i) => {
            const faIcon = document.createElement("div");
            faIcon.innerHTML = '<i class="fa-sharp fa-solid fa-location-dot"></i>';
            const faPin = new PinElement({
                glyph: faIcon,
                glyphColor: '#ff8300',
                background: '#ffd514',
                borderColor: '#ff8300'
            });
            const marker = new AdvancedMarkerElement({
                position: {
                    lat: parseFloat(i.lat),
                    lng: parseFloat(i.lng)
                },
                map,
                content: faPin.element,
                title: i.name,
                gmpClickable: true,
                gmpDraggable: true,
            });
            const node = document.createElement('div');
            node.innerHTML =
                '<div class="input-group mb-3"></div>' +
                '<p class="address"><strong>'+ i.name + '</strong></br>'+
                i.address + '</br>' +
                '</p>';

            marker.addListener("click", ({ domEvent, latLng }) => {
                infoWindow.close();
                infoWindow.setContent(node);
                infoWindow.open(marker.map, marker);
                map.setZoom(16);
                map.setCenter(latLng);
            });

            marker.addListener('dragend',(event)=> {
               const position = marker.position;
               node.innerHTML =
                '<form class="input-validation-required" method="POST" action="{% url 'fiberPlan:updatevault' %}">{% csrf_token %}<table>' +
                '<tr><td colspan="2"><wa-input id="updateName" name="name" size="small" value="' + i.name + '" clearable required></wa-input></td></tr>' +
                '<tr><td colspan="2"><wa-input id="updateAddress" name="address" placeholder="Address" size="small" clearable required></wa-input></td></tr>' +
                '<tr><td colspan="2"><wa-input id="updateNotes" placeholder="Notes" name="notes" size="small" value="' + i.notes + '" clearable required></wa-input></td></tr>' +
                '<tr><td><wa-checkbox size="small" id="updateEnc" name="is_enc">Enclosure</wa-checkbox></td>' +
                '<td rowspan="2"><wa-button size="small" appearance="tinted" variant="success" onclick="updateVault()">Update Vault</wa-button><br /></td></tr>' +
                '<tr><td><wa-checkbox name="is_mini" id="updateMini" size="small">Mini Vault</wa-checkbox></td></tr>' +
                '<tr><td colspan="2">' +
                '<input type="hidden" id="updateID" name="id" value="' + i.id + '">' +
                '<input type="hidden" id="updateLat" name="lat" value="' + position.lat + '">' +
                '<input type="hidden" id="updateLng" name="lng" value="' + position.lng + '">' +
                '</td></tr></table></form><br />';
               $.ajax({
                url: "{% url 'fiberPlan:jsonresponse' %}",
                method: 'GET',
                data: {
                    param: 'geocodeLatLng',
                    lat: position.lat,
                    lng: position.lng,
                },
                success: function(data){
                    $('#updateAddress').val(data[0].address_components[0].long_name + ' ' + data[0].address_components[1].long_name + ' ' +
                    data[0].address_components[3].long_name + ', ' + data[0].address_components[4].short_name + ' ' + data[0].address_components[6].long_name);
                }
               });
               infoWindow.close();
               infoWindow.setContent(node);
               infoWindow.open(marker.map, marker);
            });
            return marker;
        }) || [];

        // Create customer markers
        customerMarkers = data['customer']?.map((i) => {
            //console.log('lat ' + i.lat + ' lng ' + i.lng)
            const faIcon = document.createElement("div");
            faIcon.innerHTML = '<i class="fa-solid fa-user"></i>';
            const faPin = new PinElement({
                glyph: faIcon,
                glyphColor: '#ffffff',
                background: i.isCustomer ? '#28a745' : '#007bff',
                borderColor: i.isCustomer ? '#1e7e34' : '#0056b3'
            });

            const marker = new AdvancedMarkerElement({
                position: {
                    lat: parseFloat(i.lat),
                    lng: parseFloat(i.lng)
                },
                map,
                content: faPin.element,
                title: i.firstName + ' ' + i.lastName + ', ' + i.street1,
                gmpClickable: true,
            });

            const node = document.createElement('div');
            node.innerHTML =
                '<div class="input-group mb-3"></div>' +
                '<p class="address"><strong>'+ i.firstName + ' ' + i.lastName + '</strong></br>' +
                i.street1 + '</br>' +
                i.city + '</br>' +
                '<span class="badge ' + (i.isCustomer ? 'bg-success' : 'bg-primary') + '">' +
                (i.isCustomer ? 'Customer: ' + i.clientId : 'Lead') + '</span>' +
                '</p>';

            marker.addListener("click", ({ domEvent, latLng }) => {
                infoWindow.close();
                infoWindow.setContent(node);
                infoWindow.open(marker.map, marker);
                map.setZoom(16);
                map.setCenter(latLng);
            });

            return marker;
        }) || [];

        // Add map click listener for new vault creation
        map.addListener("click",(mapsMouseEvent) => {
            infoWindow.close();
            var node = document.createElement('div');
            node.innerHTML =
                '<form class="input-validation-required" method="POST" action="{% url 'fiberPlan:savevault' %}">{% csrf_token %}<table>' +
                '<tr><td colspan="2"><wa-input placeholder="Vault Name" name="name" size="small" clearable required></wa-input></td></tr>' +
                '<tr><td colspan="2"><wa-input placeholder="Notes" name="notes" size="small" clearable required></wa-input></td></tr>' +
                '<tr><td><wa-checkbox size="small" name="is_enc">Enclosure</wa-checkbox></td>' +
                '<td rowspan="2"><wa-button size="small" appearance="tinted" variant="success" type="submit">Save</wa-button><br /></td></tr>' +
                '<tr><td><wa-checkbox name="is_mini" size="small">Mini Vault</wa-checkbox></td></tr>' +
                '<tr><td colspan="2">' +
                '<input type="hidden" name="address" id="inputAddress">' +
                '<input type="hidden" name="lat" value="' + mapsMouseEvent.latLng.lat() + '">' +
                '<input type="hidden" name="lng" value="' + mapsMouseEvent.latLng.lng() + '">' +
                '</td></tr></table></form><br />' +
                '<p class="address"></p>';

            $.ajax({
                url: "{% url 'fiberPlan:jsonresponse' %}",
                method: 'GET',
                data: {
                    param: 'geocodeLatLng',
                    lat: mapsMouseEvent.latLng.lat(),
                    lng: mapsMouseEvent.latLng.lng(),
                },
                success: function(data){
                    $('.address').html(data[0].address_components[0].long_name + ' ' + data[0].address_components[1].long_name + '</br><strong>' +
                    data[0].address_components[3].long_name + ', ' + data[0].address_components[4].short_name + ' ' + data[0].address_components[6].long_name + '</strong>')
                    $('#inputAddress').val(data[0].address_components[0].long_name + ' ' + data[0].address_components[1].long_name + ' ' +
                    data[0].address_components[3].long_name + ', ' + data[0].address_components[4].short_name + ' ' + data[0].address_components[6].long_name);
                }
            });

            infoWindow = new google.maps.InfoWindow({
               position: mapsMouseEvent.latLng,
               content: node,
            });

            infoWindow.open(map);
        });

        // Add toggle button event listeners
        document.getElementById('toggleFiber').addEventListener('click', function() {
            fiberLinesVisible = !fiberLinesVisible;
            map.data.setStyle({
                visible: fiberLinesVisible,
                strokeColor: 'red',
                strokeWeight: 4
            });
        });

        document.getElementById('toggleVaults').addEventListener('click', function() {
            vaultMarkersVisible = !vaultMarkersVisible;
            vaultMarkers.forEach(marker => {
                marker.map = vaultMarkersVisible ? map : null;
            });
        });

        document.getElementById('toggleCustomers').addEventListener('click', function() {
            customersVisible = !customersVisible;
            customerMarkers.forEach(marker => {
                marker.map = customersVisible ? map : null;
            });
        });
    }

    function updateVault(){
        $.ajax({
            url: "{% url 'fiberPlan:jsonresponse' %}",
            method: 'GET',
            data: {
                param:      'updatevault',
                name:       $("#updateName").val(),
                address:    $("#updateAddress").val(),
                notes:     $("#updateNotes").val(),
                id:        $("#updateID").val(),
                is_enc:    $("#updateEnc").val(),
                is_mini:   $("#updateMini").val(),
                lat:       $("#updateLat").val(),
                lng:       $("#updateLng").val(),
            },
            success: function(data){
                console.log('saved vault');
            }
        });
        return true;
    }
    </script>

    {% include "bootstrap/navi.html" %}
    <div class="row mx-2">
        <div class="col"><h1 class="display-6">Camden NC Vaults</h1></div>
    </div>
    <div class="row">
        <div class="col">
            <div class="mapholder">
                <div class="row mx-2 mb-2">
                    <div class="col">
                        <div class="btn-group" role="group" aria-label="Map toggles">
                            <button type="button" class="btn btn-primary" id="toggleFiber">Show/Hide Fiber Lines</button>
                            <button type="button" class="btn btn-primary" id="toggleVaults">Show/Hide Vaults</button>
                            <button type="button" class="btn btn-primary" id="toggleCustomers">Show/Hide Customers</button>
                        </div>
                    </div>
                </div>
                <div id="map" style="width: 100%; height: 90%; min-height: 900px"></div>
            </div>
            <script>
              (g=>{var h,a,k,p="The Google Maps JavaScript API",c="google",l="importLibrary",q="__ib__",m=document,b=window;b=b[c]||(b[c]={});var d=b.maps||(b.maps={}),r=new Set,e=new URLSearchParams,u=()=>h||(h=new Promise(async(f,n)=>{await (a=m.createElement("script"));e.set("libraries",[...r]+"");for(k in g)e.set(k.replace(/[A-Z]/g,t=>"_"+t[0].toLowerCase()),g[k]);e.set("callback",c+".maps."+q);a.src=`https://maps.${c}apis.com/maps/api/js?`+e;d[q]=f;a.onerror=()=>h=n(Error(p+" could not load."));a.nonce=m.querySelector("script[nonce]")?.nonce||"";m.head.append(a)}));d[l]?console.warn(p+" only loads once. Ignoring:",g):d[l]=(f,...n)=>r.add(f)&&u().then(()=>d[l](f,...n))})({
                key: "{{ key }}",
                v: "weekly",
              });
            </script>
        </div>
    </div>
{% endblock content %}