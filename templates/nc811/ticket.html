{% extends "bootstrap/base.html" %}
{% load static %}
{% block content %}
    <style>
        table.table th,
        table.table td {
            padding: 0.3rem; /* Reduce padding */
            line-height: 1; /* Reduce line spacing */
        }

        table.table-sm th,
        table.table-sm td {
            padding: 0.2rem; /* Even smaller padding for 'sm' table */
        }
    </style>
    <div class="pageholder">
        {% include "google/../bootstrap/navi.html" %}
        <div class="row mx-2">
            <div class="col-4">
                <!-- Emergency Ticket Highlight -->
                {% if ticket.emergency %}
                    <h1 class="fs-3 text-danger">NC811 EMERGENCY Ticket: {{ ticket.ticket }}
                        <span style="font-size: 1em;">REV: {{ ticket.revision }}</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    </h1>
                {% else %}
                    <h1 class="fs-3">NC811 Ticket: {{ ticket.ticket }}
                        <span style="font-size: 1em;">REV: {{ ticket.revision }}</span>&nbsp;&nbsp;&nbsp;&nbsp;
                    </h1>
                {% endif %}
            </div>
            <div class="col-8">
                <button id="printButton" class="btn btn-primary">Print Ticket</button>
            </div>
        </div>
        <div class="row mx-2">
            <div class="col-4">
                <table class="table table-striped table-hover"
                       style="font-size: 0.7em; line-height: 1; padding: 0.2rem;">

                    <tr>
                        <td colspan="2"><b>Ticket Information</b></td>
                    </tr>
                    <tr>
                        <td>Ticket Number:</td>
                        <td><a href="/nc811/ticket/{{ ticket.original_ticket }}">{{ ticket.ticket }}</a></td>
                    </tr>
                    <tr>
                        <td>Update Of:</td>
                        <td><a href="/nc811/ticket/{{ ticket.original_ticket }}">{{ ticket.original_ticket }}</a></td>
                    </tr>
                    <tr>
                        <td>Previous Status:</td>
                        <td>{{ ticket.previous_status }}</td>
                    </tr>
                    <tr>
                        <td>Update Count:</td>
                        <td>{{ ticket.update_count }}</td>
                    </tr>
                    <tr>
                        <td>Original Call Date:</td>
                        <td>{{ ticket.original_date }}</td>
                    </tr>
                    <tr>
                        <td>Start Work Date:</td>
                        <td>{{ ticket.started }}</td>
                    </tr>
                    <tr>
                        <td>Expiration Date:</td>
                        <td>{{ ticket.expires }}</td>
                    </tr>
                    <tr>
                        <td>Release Time:</td>
                        <td>{{ ticket.release_time }}</td>
                    </tr>
                    <tr>
                        <td>Type:</td>
                        <td>{{ ticket.type }}</td>
                    </tr>
                    <tr>
                        <td>OP/ REV OP:</td>
                        <td>{{ ticket.op_rev_op }}</td>
                    </tr>
                    <tr>
                        <td>Past Work Start:</td>
                        <td>{{ ticket.past_work_start }}</td>
                    </tr>
                    <tr>
                        <td>Locked:</td>
                        <td>{{ ticket.locked }}</td>
                    </tr>
                    <tr>
                        <td>Past Due Time:</td>
                        <td>{{ ticket.past_due_time }}</td>
                    </tr>
                </table>
                <!-- Excavator Information Table -->
                <table class="table table-striped table-hover"
                       style="font-size: 0.8em; line-height: 1.1; padding: 0.4rem;">

                    <tr>
                        <td colspan="2"><b>Excavator Information</b></td>
                    </tr>
                    <tr>
                        <td>Company:</td>
                        <td>{{ ticket.name }}</td>
                    </tr>
                    <tr>
                        <td>Address:</td>
                        <td>{{ ticket.address1 }} {{ ticket.city }} {{ ticket.cstate }}, {{ ticket.zip }}</td>
                    </tr>
                    <tr>
                        <td>Caller:</td>
                        <td>{{ ticket.caller }}</td>
                    </tr>
                    <tr>
                        <td>Phone:</td>
                        <td>{{ ticket.caller_phone }}</td>
                    </tr>
                    <tr>
                        <td>Job Site Contact:</td>
                        <td>{{ ticket.contact }}</td>
                    </tr>
                    <tr>
                        <td>Phone:</td>
                        <td>{{ ticket.contact_phone }}</td>
                    </tr>
                    <tr>
                        <td>Email:</td>
                        <td>{{ ticket.email }}</td>
                    </tr>
                </table>
                <!-- Location Information Table -->
                <table class="table table-striped table-hover"
                       style="font-size: 0.8em; line-height: 1.1; padding: 0.4rem;">

                    <tr>
                        <td colspan="2"><b>Location Information</b></td>
                    </tr>
                    <tr>
                        <td>City/Place:</td>
                        <td>
                            {% if ticket.county == ticket.place %}
                                {{ ticket.county }}
                            {% else %}
                                {{ ticket.county }} {{ ticket.place }}
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td>Address:</td>
                        {% if ticket.st_from_address == ticket.st_to_address %}
                                {{ ticket.st_from_address }}
                            {% else %}
                                {{ ticket.st_from_address }} - {{ ticket.st_to_address }}
                            {% endif %}
                    </tr>
                    <tr>
                        <td>Street:</td>
                        <td>{{ ticket.st_from_address }} - {{ ticket.st_to_address }} {{ ticket.street }}</td>
                    </tr>
                    <tr>
                        <td>Intersecting Street:</td>
                        <td>{{ ticket.cross1 }}</td>
                    </tr>
                    <tr>
                        <td>Extent of Work:</td>
                        <td>{{ ticket.extent_of_work }}</td>
                    </tr>
                    <tr>
                        <td>Comments:</td>
                        <td>{{ ticket.location }}</td>
                    </tr>
                    <tr>
                        <td><b>Map Coordinates:</b></td>
                    </tr>
                    <tr>
                        <td>NW Lat:</td>
                        <td>{{ ticket.extent_top }}</td>
                    </tr>
                    <tr>
                        <td>SE Lat:</td>
                        <td>{{ ticket.extent_bottom }}</td>
                    </tr>
                </table>

            </div>

            <!-- Google Map -->
            <div class="col">
                <div class="mapholder">
                    <div id="map" style="width: 100%; height: 800px"></div>
                    <script>
                        (g => {
                            let h, a, k, p = "The Google Maps JavaScript API", c = "google", l = "importLibrary",
                                q = "__ib__", m = document, b = window;
                            b = b[c] || (b[c] = {});
                            let d = b.maps || (b.maps = {}), r = new Set, e = new URLSearchParams,
                                u = () => h || (h = new Promise(async (f, n) => {
                                    await (a = m.createElement("script"));
                                    e.set("libraries", [...r] + "");
                                    for (k in g) e.set(k.replace(/[A-Z]/g, t => "_" + t[0].toLowerCase()), g[k]);
                                    e.set("callback", c + ".maps." + q);
                                    a.src = `https://maps.${c}apis.com/maps/api/js?` + e;
                                    d[q] = f;
                                    a.onerror = () => h = n(Error(p + " could not load."));
                                    a.nonce = m.querySelector("script[nonce]")?.nonce || "";
                                    m.head.append(a)
                                }));
                            d[l] ? console.warn(p + " only loads once. Ignoring:", g) : d[l] = (f, ...n) => r.add(f) && u().then(() => d[l](f, ...n))
                        })({
                            key: "{{ key }}",
                            v: "weekly",
                            // Use the 'v' parameter to indicate the version to use (weekly, beta, alpha, etc.).
                            // Add other bootstrap parameters as needed, using camel case.
                        });
                    </script>
                </div>
            </div>
        </div>
        <div class="row mx-2">
            <div class="col-12">
                <table id="responses_table" class="table table-striped table-hover"
                       style="font-size: 0.7em; line-height: 1; padding: 0.2rem;">
                    <thead>
                    <tr>
                        <th>MB Code</th>
                        <th>Name</th>
                        <th>Response</th>
                        <th>Description</th>
                        <th>Responded</th>
                </table>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function () {
            let printable_ticket = '{{ print_ticket_json }}';
            let ticketResponseDue = new Date('{{ ticket.response_due }}');
            let pastDue = (Date.now() > ticketResponseDue);

            const statusOptions = [
                {
                    esvc: 'Select A Status',
                    nc811: 'Select A Status',
                    statusCode: "0"
                },
                {
                    esvc: 'No conflict, utility is outside of stated work area',
                    nc811: 'No conflict; utility is outside of the stated work area.',
                    statusCode: "10"
                },
                {
                    esvc: 'Marked',
                    nc811: 'Marked; the utility has marked the location of its underground facilities.',
                    statusCode: "20"
                },
                {esvc: 'Not complete', nc811: 'Not complete; the locate request is incomplete.', statusCode: "30"},
                {
                    esvc: 'Locate not complete, additional communication required, unable to contact excavator',
                    nc811: 'Locate not complete; additional communication with the excavator is required, and the locator was unable to contact the excavator.',
                    statusCode: "32"
                },
                {
                    esvc: 'Could not gain access',
                    nc811: 'Could not gain access to the property; the locator will contact the excavator.',
                    statusCode: "40"
                },
                {
                    esvc: 'Railroad facility not marked',
                    nc811: 'Railroad facility not marked; excavation on railroad property requires appropriate permits from the railroad owner/operator.',
                    statusCode: "45"
                },
                {
                    esvc: 'Critical facility not marked',
                    nc811: 'Critical facility not marked; the utility owner or representative must be present during excavation to identify unmarked facilities.',
                    statusCode: "50"
                },
                {
                    esvc: 'Critical facility marked',
                    nc811: 'Critical facility marked; the utility owner or representative must be present during excavation.',
                    statusCode: "55"
                },
                {
                    esvc: 'Marking schedule agreed',
                    nc811: 'Locator and excavator have agreed upon and documented a marking schedule.',
                    statusCode: "60"
                },
                {
                    esvc: 'Excavation completed early',
                    nc811: 'Excavator completed work prior to the due date.',
                    statusCode: "70"
                },
                {
                    esvc: 'Master contractor responsible',
                    nc811: 'Member’s master contractor is responsible for locating facilities.',
                    statusCode: "80"
                },
                {
                    esvc: 'Survey request, facilities marked',
                    nc811: 'Survey design request—facility has been marked in the field.',
                    statusCode: "90"
                },
                {
                    esvc: 'Survey request, no facilities',
                    nc811: 'Survey design request—no facilities in the area.',
                    statusCode: "92"
                },
                {
                    esvc: 'Survey request, records provided',
                    nc811: 'Survey design request—facility records provided.',
                    statusCode: "94"
                },
                {
                    esvc: 'Survey request, access to records',
                    nc811: 'Survey design request—access to facility records provided.',
                    statusCode: "98"
                },
                {
                    esvc: 'Request denied, Homeland Security',
                    nc811: 'Location request denied due to Homeland Security concerns; the utility operator needs to confirm the legitimacy of the proposed excavation and may require additional information.',
                    statusCode: "100"
                },
                {
                    esvc: 'Sub-aqueous facilities present',
                    nc811: 'Sub-aqueous facilities present; the utility owner will locate facilities within 10 full working days.',
                    statusCode: "110"
                },
                {
                    esvc: 'Extraordinary circumstances',
                    nc811: 'Extraordinary circumstances exist; the utility owner is unable to complete the locate request until a specified date and time.',
                    statusCode: "888"
                },
                {
                    esvc: 'No response by required time',
                    nc811: 'Member has not responded by the required time.',
                    statusCode: "999"
                }
            ];

            let lastStatus = null;

            function isNullOrEmpty(str) {
                return (typeof str === 'string' && str.trim().length === 0) || str === null;
            }


            // Initialize DataTable
            $("#responses_table").DataTable({
                ajax: {
                    processing: true,
                    url: "{% url 'NC811:jsonresponse' %}",
                    data: {
                        param: "get_ticket_responses",
                        ticket: "{{ ticket.ticket }}",
                    },
                    dataSrc: "",
                },
                paging: false,
                layout: {topEnd: null},
                columns: [
                    {data: 'mbcode'},
                    {data: 'name'},
                    {data: 'response', render: data => data ? data : 'Pending'},
                    {
                        data: 'description',
                        render: function (data, type, row) {

                           if ({{ ticket.isPastDue }} || !row.mbcode.includes("ESC01")) return data;
                            let dropdown = '<select class="form-select">';
                                 statusOptions.forEach(statusInfo => {
                                    let statusCode = statusInfo.statusCode;
                                    let rowStatusCode = (row.response) ? row.response : 0;

                                    let selected = (rowStatusCode === statusCode) ? 'selected' : 0;
                                    dropdown += `<option value="${statusInfo.statusCode}" ${selected}>${statusInfo.esvc}</option>`;
                                });
                            dropdown += '</select><button id="updateButton" type="button" style="height: 33px; padding: 5px 20px;"  disabled>Update</button>';

                            lastStatus = row.response;
                            return dropdown;
                        }
                    },
                    {data: 'responded', render: data => data ? data : ''}
                ]
            });

            // Fetch map data and initialize the map
            $.ajax({
                url: "{% url 'NC811:jsonresponse'%}",
                data: {param: 'get_map_data'},
                method: 'GET',
                success: function (data) {
                    initMap(data);
                }
            });

            let map;

            async function initMap(data) {
                const {Map} = await google.maps.importLibrary("maps");
                const {AdvancedMarkerElement} = await google.maps.importLibrary("marker");

                map = new Map(document.getElementById("map"), {
                    center: {lat: {{ ticket.extent_top }}, lng: {{ ticket.extent_left }}},
                    zoom: 17,
                    mapTypeId: 'roadmap',
                    mapId: "9e3cc3275c46848c",
                });

                map.data.loadGeoJson("{% static 'geodata/camden_fiber.geojson' %}");
                map.data.setStyle({strokeColor: 'yellow', strokeWeight: 3});

                data?.forEach(i => {
                    const marker = new AdvancedMarkerElement({
                        position: {lat: parseFloat(i.lat), lng: parseFloat(i.lng)},
                        map,
                        title: i.name,
                    });
                    marker.addListener("click", () => {
                        map.setZoom(14);
                        map.setCenter(marker.getPosition());
                    });
                });

                new google.maps.Rectangle({
                    strokeColor: "#FF0000",
                    strokeOpacity: 0.8,
                    strokeWeight: 2,
                    fillColor: "#FF0000",
                    fillOpacity: 0.35,
                    map,
                    bounds: {
                        north: {{ ticket.extent_top }},
                        south: {{ ticket.extent_bottom }},
                        east: {{ ticket.extent_right }},
                        west: {{ ticket.extent_left }},
                    },
                });

                new google.maps.Polygon({
                    paths: {% autoescape off %}{{ polygon }}{% endautoescape %},
                    strokeColor: "#04668e",
                    strokeOpacity: 0.8,
                    strokeWeight: 3,
                    fillColor: "#c0fbfb",
                    fillOpacity: 0.35,
                    map,
                }).setMap(map);
            }

            // Handle dropdown change event
            $('#responses_table').on('change', 'select.form-select', function () {
                let row = $(this).closest('tr');
                let submitButton = row.find('button[type="button"]');
                let selectedValue = $(this).val();

                if (selectedValue !== lastStatus && selectedValue !== "0") {
                    submitButton.prop('disabled', false);
                } else {
                    submitButton.prop('disabled', true);
                    // $(this).val(originalValue);
                }

            });

            // Handle update button click event
            $("#responses_table").on("click", "button[type='button']", function () {
                const row = $(this).closest("tr");

                // Get the selected value from the dropdown
                const selectedValue = row.find("select.form-select").val();

                // Get the description value from the current row (assuming the description is in the 3rd column)
                const description = row.find("td:eq(2)").text().trim();  // Adjust the index if needed

                // Call the processUpdate function with both the selected value (status code) and description
                processUpdate(selectedValue, description);
            });


            function processUpdate(status_id, description) {
                let currentTicket = '{{ ticket.original_ticket }}';
                updateResponse(currentTicket, description, status_id);
            }


            function updateResponse(ticket_id, response_id, status) {
                $.ajax({
                    url: "/nc811/update_response_status/" + ticket_id + "/" + response_id + "/" + status + "/",
                    method: 'POST',
                    headers: {
                        "X-CSRFToken": getCSRFToken(), // Adjust if CSRF token is required
                    },
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    success: function (response) {
                        // Show success message using a popup alert
                        alert(response.message || response || "Response updated successfully!");
                        lastStatus = status;
                        let updateButton = document.getElementById("updateButton");
                        updateButton.disabled = true;
                    },
                    error: function (xhr) {
                        // Show the error message from the server (if available) using a popup alert
                        let errorMessage = "An error occurred. Please try again.";

                        alert(xhr);
                    }
                });
            }


            function getCSRFToken() {
                let cookies = document.cookie.split(';');
                for (let cookie of cookies) {
                    let [name, value] = cookie.trim().split('=');
                    if (name === "csrftoken") return value;
                }
                return "";
            }


            document.getElementById("printButton").addEventListener("click", function () {
                let ticketNumber = "{{ ticket.ticket }}";  // Get the ticket number from Django template
                let printUrl = `/nc811/get_printable_ticket/${ticketNumber}`; // Construct the print page URL

                // Open a new popup window for printing
                let printWindow = window.open(printUrl, "PrintTicket", "width=800,height=600");

                if (printWindow) {
                    printWindow.focus(); // Bring the window to the front
                } else {
                    alert("Popup blocked! Please allow popups for this site.");
                }
            });

git
        });

    </script>

{% endblock %}