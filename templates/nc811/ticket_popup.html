{% extends "bootstrap/base.html" %}
{% load static %}
{% block content %}
    <div class="pageholder">
        <div class="row mx-2">
            <div class="col">

                <!-- Ticket Details Table -->
                <table class="table table-striped table-hover">
                    <tr>
                        <td>Original Ticket: <a
                                href="/nc811/ticket/{{ ticket.original_ticket }}">{{ ticket.original_ticket }}</a></td>
                        <td>Original Date: {{ ticket.original_date }}</td>
                    </tr>
                    <tr>
                        <td>{{ ticket.name }}</td>
                        <td>Type: {{ ticket.caller_type }}</td>
                    </tr>
                    <tr>
                        <td>{{ ticket.address1 }}</td>
                        <td>{{ ticket.city }} {{ ticket.state }}, {{ ticket.zip }}</td>
                    </tr>
                    <tr>
                        <td>Caller: {{ ticket.caller }}</td>
                        <td>Contact: {{ ticket.contact }}</td>
                    </tr>
                    <tr>
                        <td>Cell: {{ ticket.caller_phone }}</td>
                        <td>Cell: {{ ticket.contact_phone }}</td>
                    </tr>
                    <tr>
                        <td>Email: {{ ticket.caller_email }}</td>
                        <td>Sub: {{ ticket.subcontractor }}</td>
                    </tr>
                    <tr>
                        <td>Work Type: {{ ticket.work_type }}</td>
                        <td>Duration: {{ ticket.duration }}</td>
                    </tr>
                    <tr>
                        <td>Street</td>
                        <td>{{ ticket.st_from_address }} - {{ ticket.st_to_address }} {{ ticket.street }}</td>
                    </tr>
                    <tr>
                        <td>Cross</td>
                        <td>{{ ticket.cross1 }}</td>
                    </tr>
                    <tr>
                        <td colspan="2">{{ ticket.location }}</td>
                    </tr>
                </table>


            <!-- Google Map -->
            <div class="col">
                <div class="mapholder">
                    <div id="map" style="width: 100%; height: 500px"></div>
                    <script>
                        (g => {
                            let h, a, k, p = "The Google Maps JavaScript API", c = "google", l = "importLibrary",
                                q = "__ib__", m = document, b = window;
                            b = b[c] || (b[c] = {});
                            let d = b.maps || (b.maps = {}), r = new Set, e = new URLSearchParams,
                                u = () => h || (h = new Promise(async (f, n) => {
                                    await (a = m.createElement("script"));
                                    e.set("libraries", [...r] + "");
                                    for (k in g) e.set(k.replace(/[A-Z]/g, t => "_" + t[0].toLowerCase()), g[k]);
                                    e.set("callback", c + ".maps." + q);
                                    a.src = `https://maps.${c}apis.com/maps/api/js?` + e;
                                    d[q] = f;
                                    a.onerror = () => h = n(Error(p + " could not load."));
                                    a.nonce = m.querySelector("script[nonce]")?.nonce || "";
                                    m.head.append(a)
                                }));
                            d[l] ? console.warn(p + " only loads once. Ignoring:", g) : d[l] = (f, ...n) => r.add(f) && u().then(() => d[l](f, ...n))
                        })({
                            key: "{{ key }}",
                            v: "weekly",
                            // Use the 'v' parameter to indicate the version to use (weekly, beta, alpha, etc.).
                            // Add other bootstrap parameters as needed, using camel case.
                        });
                    </script>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function () {

     // Fetch map data and initialize the map
            $.ajax({
                url: "{% url 'NC811:jsonresponse'%}",
                data: {param: 'get_map_data'},
                method: 'GET',
                success: function (data) {
                    initMap(data);
                }
            });
            let map;

            async function initMap(data) {
                const {Map} = await google.maps.importLibrary("maps");
                const {AdvancedMarkerElement} = await google.maps.importLibrary("marker");

                map = new Map(document.getElementById("map"), {
                    center: {lat: {{ ticket.extent_top }}, lng: {{ ticket.extent_left }}},
                    zoom: 17,
                    mapTypeId: 'roadmap',
                    mapId: "9e3cc3275c46848c",
                });

                map.data.loadGeoJson("{% static 'geodata/camden_fiber_0525.geojson' %}");
                map.data.setStyle({strokeColor: 'yellow', strokeWeight: 3});

                data?.forEach(i => {
                    const marker = new AdvancedMarkerElement({
                        position: {lat: parseFloat(i.lat), lng: parseFloat(i.lng)},
                        map,
                        title: i.name,
                    });
                    marker.addListener("click", () => {
                        map.setZoom(14);
                        map.setCenter(marker.getPosition());
                    });
                });

                new google.maps.Rectangle({
                    strokeColor: "#FF0000",
                    strokeOpacity: 0.8,
                    strokeWeight: 2,
                    fillColor: "#FF0000",
                    fillOpacity: 0.35,
                    map,
                    bounds: {
                        north: {{ ticket.extent_top }},
                        south: {{ ticket.extent_bottom }},
                        east: {{ ticket.extent_right }},
                        west: {{ ticket.extent_left }},
                    },
                });

                new google.maps.Polygon({
                    paths: {% autoescape off %}{{ polygon }}{% endautoescape %},
                    strokeColor: "#04668e",
                    strokeOpacity: 0.8,
                    strokeWeight: 3,
                    fillColor: "#c0fbfb",
                    fillOpacity: 0.35,
                    map,
                }).setMap(map);
            }


        });
    </script>
    </div>
{% endblock %}