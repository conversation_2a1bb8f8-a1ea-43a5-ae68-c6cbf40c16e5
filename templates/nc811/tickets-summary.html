{%  extends "bootstrap/base.html" %}
{% block content %}
    {% csrf_token %}
    <div class="pageholder">
        <div class="row mx-2">
            <div class="col">
                <div class="d-flex align-items-center mb-3">
                    <h1 class="display-6 me-3">NC811 Tickets in past 7 days</h1>
                    <button id="updateNowBtn" class="btn btn-primary">Update Now</button>
                </div>
                <p>Last Update: <span>{{ ticket_updated }}</span></p>
            </div>
        </div>
        <div class="row mx-2">
            <div class="col">                
                <table id="ticket_table" class="table table-striped table-hover" >
                    <thead>
                    <tr>
                        <th>Ticket</th>
                        <th>Priority</th>
                        <th>Street</th>
                        <th>Cross</th>
                        <th>Place</th>
                        <th>For</th>
                        <th>Type</th>
                        <th>Resp Due</th>
                        <th>Work Date</th>                        
                    </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
<script>
    $(document).ready(function() {

        // Handle Update Now button click
        $('#updateNowBtn').click(function() {
            var $btn = $(this);
            var originalText = $btn.text();

            // Disable button and show loading state
            $btn.prop('disabled', true).text('Updating...');

            // Make AJAX request to update tickets
            $.ajax({
                url: "{% url 'NC811:update_tickets_now' %}",
                type: 'POST',
                headers: {
                    'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
                },
                success: function(response) {
                    if (response.success) {
                        alert(response.message);
                        // Refresh the page to show updated data
                        location.reload();
                    } else {
                        alert('Error: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    alert('Error updating tickets: ' + error);
                },
                complete: function() {
                    // Re-enable button and restore original text
                    $btn.prop('disabled', false).text(originalText);
                }
            });
        });

        $('#ticket_table').DataTable({
           ajax: {
              processing: true,
              url: "{% url 'NC811:jsonresponse' %}",
                data: {
                  param: "get_tickets"
               },
               dataSrc: "",
            },
            order: [[7, 'desc']],
            lengthMenu: [
                [25,50,-1],
                [25,50,'ALL']
            ],
            columnDefs: [
                {
                    targets: -1,
                    className: 'dt-body-left'
                }
            ],
            columns: [
                    { data: "ticket" , render(data){
                        return '<a class="ticket" data-bs-toggle="tooltip" data-bs-placement="top" title="' + data +'" href="/nc811/ticket/' + data + '">' + data + '</a>'
                        }},
                    { data: "priority"},
                    { data: "street"},
                    { data: "cross"},
                    { data: "place"},
                    { data: "done_for"},
                    { data: "work_type"},                    
                    { data: "response_due", render(data){
                        var now = new Date()
                        var respdue_date = new Date(data)
                        if (respdue_date > now) {
                            return '<span style="color:green">' + data + '</span>';
                        }
                        else
                        {
                            return '<span style="color: red">' + data + '</span>';
                            }                            
                        }},
                    { data: "work_date", render(data){
                        var now = new Date()
                        var work_date = new Date(data)
                        if (work_date > now) {
                            return '<span style="color:green">' + data + '</span>';
                        }
                        else
                        {
                            return '<span style="color: red">' + data + '</span>';
                            }                            
                        }},
                ],
        });
    } );
</script>
{% endblock %}