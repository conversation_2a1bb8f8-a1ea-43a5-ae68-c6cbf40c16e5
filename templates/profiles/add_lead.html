{# This is the form partial included inside the modal-body of lead_management.html #}

<div class="form-group mb-3">
    <label for="id_firstName">First Name</label>
    <input type="text"
           class="form-control"
           id="id_firstName"
           name="firstName"
           required>
</div>

<div class="form-group mb-3">
    <label for="id_lastName">Last Name</label>
    <input type="text"
           class="form-control"
           id="id_lastName"
           name="lastName"
           required>
</div>

<div class="form-group mb-3">
    <label for="id_email">Email</label>
    <input type="email"
           class="form-control"
           id="id_email"
           name="email"
           required>
</div>

<div class="form-group mb-3">
    <label for="id_street1">Street Address</label>
    <input type="text"
           class="form-control"
           id="id_street1"
           name="street1">
</div>

<div class="form-group mb-3">
    <label for="id_city">City</label>
    <input type="text"
           class="form-control"
           id="id_city"
           name="city">
</div>

<div class="row">
    <div class="col">
        <div class="form-group mb-3">
            <label for="id_lat">Latitude</label>
            <input type="number"
                   step="0.00000001"
                   class="form-control"
                   id="id_lat"
                   name="lat"
                   placeholder="Auto-filled from address">
        </div>
    </div>
    <div class="col">
        <div class="form-group mb-3">
            <label for="id_lng">Longitude</label>
            <input type="number"
                   step="0.00000001"
                   class="form-control"
                   id="id_lng"
                   name="lng"
                   placeholder="Auto-filled from address">
        </div>
    </div>
    <div class="col-auto">
        <div class="form-group mb-3">
            <label>&nbsp;</label>
            <button type="button"
                    class="btn btn-outline-primary d-block"
                    id="manual-geocode-btn"
                    title="Manually geocode address">
                <i class="fas fa-map-marker-alt"></i>
            </button>
        </div>
    </div>
</div>

<div class="alert alert-info" role="alert">
    <small><i class="fas fa-info-circle"></i>
    <strong>Auto-Geocoding:</strong> Enter a complete street address and city above.
    The latitude and longitude will be automatically populated using Google Maps.
    </small>
</div>

{# Hidden fields to mark this as a lead-only record #}
<input type="hidden" name="isLead" value="True">
<input type="hidden" name="isCustomer" value="False">
