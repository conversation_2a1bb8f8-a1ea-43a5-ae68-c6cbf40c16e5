{% extends "bootstrap/base.html" %}
{% load static %}
{% block content %}
    <div class="container mt-4">
        {% include "bootstrap/navi.html" %}
        <div class="card">
            <div class="card-header">
                <h1>Audit Log</h1>
            </div>
            <div class="card-body">
                <div class="col-12">
                    <table id="audit_table" class="table table-striped table-hover"
                           style="font-size: 0.7em; line-height: 1; padding: 0.2rem;">
                        <thead>
                        <tr>
                            <th>Timestamp</th>
                            <th>User</th>
                            <th>Model</th>
                            <th>Object ID</th>
                            <th>Action</th>
                            <th>Extra Data</th>
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <script>
        $(document).ready(function () {
            // Initialize DataTable with columns matching the JSON keys
            $("#audit_table").DataTable({
                ajax: {
                    processing: true,
                    url: "{% url 'audit_json' %}",
                    data: {},
                    dataSrc: ""
                },
                paging: false,
                columns: [
                    {data: 'timestamp'},
                    {data: 'user__username'},
                    {data: 'model_name'},
                    {data: 'object_id'},
                    {data: 'action'},
                    {
                        data: 'extra_data',
                        render: function (data, type, row) {
                            // If the type is display, convert the object to a string.
                            if (type === 'display' && data) {
                                return JSON.stringify(data);
                            }
                            return data;
                        }
                    }
                ]
            });

            function getCSRFToken() {
                let cookies = document.cookie.split(';');
                for (let cookie of cookies) {
                    let [name, value] = cookie.trim().split('=');
                    if (name === "csrftoken") return value;
                }
                return "";
            }
        });
    </script>
{% endblock %}
