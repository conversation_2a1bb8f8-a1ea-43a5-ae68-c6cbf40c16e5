{% extends "bootstrap/base.html" %}
{% load static %}
{% block content %}
<div class="container mt-4">
    {% include "bootstrap/navi.html" %}
    <div class="card">
        <div class="card-header">
            <h1>Change User Password</h1>
        </div>
        <div class="card-body">
            <form method="post" action="{% url 'change_user_password' %}">
                {% csrf_token %}
                <div class="form-group">
                    <label for="user_select">Select User:</label>
                    <select class="form-control" id="user_select" name="user_id" required>
                        <option value="">Choose A User</option>
                        {% for user in users %}
                            <option value="{{ user.id }}">{{ user.username }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group mt-3">
                    <label for="new_password">New Password:</label>
                    <input type="password" class="form-control" id="new_password" name="new_password" required>
                </div>
                <button type="submit" id="change_password_btn" class="btn btn-danger mt-3" disabled>Change Password</button>
            </form>
        </div>
    </div>
</div>
<script>
    document.getElementById('user_select').addEventListener('change', function() {
        var btn = document.getElementById('change_password_btn');
        btn.disabled = (this.value === "");
    });
</script>
{% endblock %}
