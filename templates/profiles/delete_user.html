{% extends "bootstrap/base.html" %}
{% load static %}
{% block content %}
<div class="container mt-4">
    {% include "bootstrap/navi.html" %}
    <div class="card">
        <div class="card-header">
            <h1>Delete User</h1>
        </div>
        <div class="card-body">
            <form method="post" action="{% url 'delete_user' %}">
                {% csrf_token %}
                <div class="form-group">
                    <label for="user_select">Select User:</label>
                    <select class="form-control" id="user_select" name="user_id" required>
                        <option value="">Choose A User</option>
                        {% for user in users %}
                            <option value="{{ user.id }}">{{ user.username }}</option>
                        {% endfor %}
                    </select>
                </div>
                <button type="submit" id="delete_user_btn" class="btn btn-danger mt-3" disabled>Delete User</button>
            </form>
        </div>
    </div>
</div>
<script>
    document.getElementById('user_select').addEventListener('change', function() {
        var btn = document.getElementById('delete_user_btn');
        btn.disabled = (this.value === "");
    });
</script>
{% endblock %}
