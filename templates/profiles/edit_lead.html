{% extends "bootstrap/base.html" %}
{% load static %}

{% block content %}
<div class="container mt-4">
    {% include "bootstrap/navi.html" %}
    <div class="card">
        <div class="card-header">
            <h1>Edit Lead</h1>
        </div>
        <div class="card-body">
            <form method="post" action="{% url 'edit_lead' lead.id %}">
                {% csrf_token %}
                <div class="form-group">
                    <label for="id_firstName">First Name:</label>
                    <input
                        type="text"
                        name="firstName"
                        id="id_firstName"
                        class="form-control"
                        value="{{ lead.firstName }}"
                        required>
                </div>
                <div class="form-group mt-3">
                    <label for="id_lastName">Last Name:</label>
                    <input
                        type="text"
                        name="lastName"
                        id="id_lastName"
                        class="form-control"
                        value="{{ lead.lastName }}"
                        required>
                </div>
                <div class="form-group mt-3">
                    <label for="id_email">Email:</label>
                    <input
                        type="email"
                        name="email"
                        id="id_email"
                        class="form-control"
                        value="{{ lead.email }}"
                        required>
                </div>
                <button type="submit" class="btn btn-primary mt-3">Save Changes</button>
                <a href="{% url 'lead_management' %}" class="btn btn-secondary mt-3 ms-2">Cancel</a>
            </form>
        </div>
    </div>
</div>
{% endblock %}
