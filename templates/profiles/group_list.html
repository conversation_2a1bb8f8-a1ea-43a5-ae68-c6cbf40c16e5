{% extends "bootstrap/base.html" %}
{% block content %}
    <div class="container mt-4">
        {% include "bootstrap/navi.html" %}
        <h1>Groups</h1>
        <div class="mb-3">
            <!-- New Link: Add User -->
            <a href="{% url 'add_user' %}" class="btn btn-primary">Add User</a>
            <!-- New Link: Change User Password -->
            <a href="{% url 'change_user_password' %}" class="btn btn-danger mx-2">Change User Password</a>
            <!-- New Link: Delete User -->
            <a href="{% url 'delete_user' %}" class="btn btn-warning mx-2">Delete User</a>
            <!-- Existing Link: Add Group -->
            <a href="{% url 'group_create' %}" class="btn btn-success">Add Group</a>
            <a href="{% url 'manage_user_groups_permissions' %}" class="btn btn-info">Manage User Groups &amp; Permissions</a>
            <a href="{% url 'audit_view' %}" class="btn btn-secondary">Audit Log</a>
            <a href="{% url 'lead_management' %}" class="btn btn-secondary mx-2">Leads</a>
        </div>
        <table class="table table-bordered">
            <tr>
                <th>Name</th>
                <th>Permissions</th>
                <th>Actions</th>
            </tr>
            {% for group in groups %}
                <tr>
                    <td>{{ group.name }}</td>
                    <td>
                        {% for perm in group.permissions.all %}
                            {{ perm.name }}<br>
                        {% endfor %}
                    </td>
                    <td>
                        <a href="{% url 'group_edit' group.id %}" class="btn btn-sm btn-primary">Edit</a>
                        <a href="{% url 'group_delete' group.id %}" class="btn btn-sm btn-danger">Delete</a>
                    </td>
                </tr>
            {% endfor %}
        </table>
    </div>
{% endblock %}
