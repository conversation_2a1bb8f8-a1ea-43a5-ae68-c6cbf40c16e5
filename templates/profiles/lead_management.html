{% extends "bootstrap/base.html" %}
{% load static %}
{% block content %}
<div class="container mt-4">
    {% include "bootstrap/navi.html" %}



    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h1 class="h3 mb-0">Lead Management</h1>
            <!-- Add New Lead button -->
            <button type="button"
                    class="btn btn-primary"
                    data-bs-toggle="modal"
                    data-bs-target="#addLeadModal">
                Add New Lead
            </button>
        </div>

        <div class="card-body">
            {% if leads %}
                <div class="table-responsive">
                  <table class="table table-striped align-middle mb-0">
                      <thead>
                          <tr>
                              <th>First Name</th>
                              <th>Last Name</th>
                              <th>Email</th>
                              <th style="width: 280px;">Actions</th>
                          </tr>
                      </thead>
                      <tbody>
                      {% for lead in leads %}
                          <tr>
                              <td>{{ lead.firstName }}</td>
                              <td>{{ lead.lastName }}</td>
                              <td>{{ lead.email|default:"None" }}</td>
                              <td>
                                  <a href="{% url 'edit_lead' lead.id %}" class="btn btn-sm btn-primary">Edit</a>
                                  <a href="{% url 'delete_lead' lead.id %}"
                                     class="btn btn-sm btn-danger"
                                     onclick="return confirm('Delete Lead?');">
                                     Delete
                                  </a>
                                  <a href="{% url 'process_signup' lead.id %}" class="btn btn-sm btn-success">
                                      Process Signup
                                  </a>
                              </td>
                          </tr>
                      {% endfor %}
                      </tbody>
                  </table>
                </div>
            {% else %}
                <p class="mb-0">No pending leads.</p>
            {% endif %}
        </div>
    </div>
</div>

<!-- Add New Lead Modal -->
<div class="modal fade" id="addLeadModal" tabindex="-1" aria-labelledby="addLeadModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="addLeadModalLabel">Add New Lead</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>

      <form method="post" action="{% url 'add_lead' %}" novalidate>
        {% csrf_token %}
        <div class="modal-body">
          {# Keep your existing partial; ensure it renders inputs with the IDs referenced below #}
          {% include "profiles/add_lead.html" %}
          {# Space where geocode status toasts will appear #}
          <div id="geocode-status-container" class="pt-1"></div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary">Add</button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
(function () {
  // Helper: safe element lookup
  function byId(id) { return document.getElementById(id); }

  // Elements expected from included partial
  const street1Input = byId('id_street1');
  const cityInput    = byId('id_city');
  const latInput     = byId('id_lat');
  const lngInput     = byId('id_lng');
  const manualBtn    = byId('manual-geocode-btn');
  const modalEl      = byId('addLeadModal');

  // If any of the required fields are missing, bail gracefully
  const geocodeReady = street1Input && cityInput && latInput && lngInput;

  let geocodeTimeout;

  function setLoadingState(loading) {
    if (!geocodeReady) return;
    const ph = loading ? 'Loading…' : '';
    latInput.placeholder = ph;
    lngInput.placeholder = ph;
    const bg = loading ? '#f8f9fa' : '';
    latInput.style.backgroundColor = bg;
    lngInput.style.backgroundColor = bg;
  }

  function showGeocodeStatus(type, message) {
    const container = document.getElementById('geocode-status-container');
    if (!container) return;

    // clear previous
    container.innerHTML = '';

    const div = document.createElement('div');
    div.className = `alert alert-${type === 'success' ? 'success' : 'warning'} alert-dismissible fade show mt-2 mb-0`;
    div.role = 'alert';
    div.innerHTML = `
      <small>${message}</small>
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    container.appendChild(div);

    if (type === 'success') {
      setTimeout(() => {
        if (div && div.parentNode) div.parentNode.removeChild(div);
      }, 3000);
    }
  }

  function geocodeAddress(address) {
    if (!geocodeReady) return;
    setLoadingState(true);

    fetch(`{% url 'geocode_address' %}?address=${encodeURIComponent(address)}`)
      .then(r => r.json())
      .then(data => {
        if (data && data.success) {
          latInput.value = Number(data.lat).toFixed(8);
          lngInput.value = Number(data.lng).toFixed(8);
          latInput.placeholder = 'Auto-filled from address';
          lngInput.placeholder = 'Auto-filled from address';
          latInput.style.backgroundColor = '#d4edda';
          lngInput.style.backgroundColor = '#d4edda';
          showGeocodeStatus('success', `Coordinates found for: ${data.formatted_address || address}`);
        } else {
          latInput.placeholder = 'Could not geocode';
          lngInput.placeholder = 'Could not geocode';
          latInput.style.backgroundColor = '#f8d7da';
          lngInput.style.backgroundColor = '#f8d7da';
          showGeocodeStatus('error', data && data.error ? data.error : 'Could not find coordinates for this address');
        }
      })
      .catch(() => {
        latInput.placeholder = 'Error geocoding';
        lngInput.placeholder = 'Error geocoding';
        latInput.style.backgroundColor = '#f8d7da';
        lngInput.style.backgroundColor = '#f8d7da';
        showGeocodeStatus('error', 'Error connecting to geocoding service');
      })
      .finally(() => setLoadingState(false));
  }

  function scheduleGeocode() {
    if (!geocodeReady) return;
    const street = street1Input.value.trim();
    const city   = cityInput.value.trim();
    if (!street || !city) return;

    clearTimeout(geocodeTimeout);
    geocodeTimeout = setTimeout(() => {
      geocodeAddress(`${street}, ${city}`);
    }, 600);
  }

  // Wire up listeners only if fields exist
  if (geocodeReady) {
    street1Input.addEventListener('input', scheduleGeocode);
    cityInput.addEventListener('input', scheduleGeocode);
  }

  if (manualBtn && geocodeReady) {
    manualBtn.addEventListener('click', function () {
      const street = street1Input.value.trim();
      const city   = cityInput.value.trim();
      if (street && city) {
        geocodeAddress(`${street}, ${city}`);
      } else {
        showGeocodeStatus('error', 'Please enter both street address and city before geocoding');
      }
    });
  }

  if (modalEl && geocodeReady) {
    modalEl.addEventListener('shown.bs.modal', function () {
      // reset visuals
      latInput.style.backgroundColor = '';
      lngInput.style.backgroundColor = '';
      document.getElementById('geocode-status-container').innerHTML = '';
      // attempt if pre-filled
      scheduleGeocode();
    });
  }
})();
</script>

{% endblock %}
