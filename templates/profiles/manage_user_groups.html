{% extends "bootstrap/base.html" %}
{% block content %}
<div class="container mt-4">
    {% include "bootstrap/navi.html" %}
    <h1>Manage User Groups & Permissions</h1>

    <form method="get" class="form-inline mb-4">
        <label for="userSelect" class="mr-2">Select User:</label>
        <select name="user_id" id="userSelect" class="form-control mr-2" onchange="this.form.submit()">
            <option value="">-- Choose User --</option>
            {% for user in users %}
                <option value="{{ user.id }}" {% if selected_user and selected_user.id == user.id %}selected{% endif %}>
                    {{ user.username }} ({{ user.email }})
                </option>
            {% endfor %}
        </select>
    </form>

    {% if selected_user %}
    <div class="card mb-4">
        <div class="card-header">
            <strong>{{ selected_user.username }}</strong> ({{ selected_user.email }})
        </div>
        <div class="card-body">
            <form method="post">
                {% csrf_token %}
                {{ form.as_p }}
                <input type="hidden" name="user_id" value="{{ selected_user.id }}">
                <button type="submit" class="btn btn-success">Update</button>
            </form>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
