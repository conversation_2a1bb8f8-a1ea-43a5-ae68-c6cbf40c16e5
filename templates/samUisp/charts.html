{% extends "bootstrap/base.html" %}
{% block content %}
    <div class="row mx-2">
        <div class="col"><h1 class="display-6">SAM/UISP</h1></div>
    </div>
    <div class="row mx-2">
        <div class="col">
            <script type="application/javascript">
                    google.load("visualization", "1", {packages:["corechart"]});
                </script>
                {{ chart_ccp.as_html }}
                {{ chart_off.as_html }}
                {{ chart_wl.as_html }}
        </div>
        <div class="col">
                {{ chart_wtn.as_html }}
                {{ chart_wts.as_html }}
        </div>
    </div>
<script>
    $(document).ready(function() {
        !function(n){n.fn.countdownTimer=function(o){let c=n.extend({seconds:5,loop:!0,callback:null},o);return this.each(function(){!function n(o,c,l,t,e){o.html(l);l--;-1===l&&t&&(l=c,null!==e&&e());l>-1?setTimeout(function(){n(o,c,l,t,e)},1e3):null!==e&&e()}(n(this),c.seconds,c.seconds,c.loop,c.callback)})}}(jQuery);        
        
        // https://www.jqueryscript.net/time-clock/countdown-timer-seconds.html
        $('.Timer').countdownTimer({
          seconds: 300,
          callback: function(){
            table1.ajax.reload()
          }});        
    });
</script>
{% endblock %}