{% extends "bootstrap/base.html" %}
{% block content %}
    <div class="row mx-2">
        <div class="col"><h1 class="display-6">CostQuest</h1></div>
    </div>
    <div class="row mx-2">
        <div class="col">             
            <table id="costquest" class="table table-striped table-hover compact" style="width: 100%">
            <thead>
            <tr>
                <th>Location</th>
                <th>Address</th>
                <th>City</th>
                <th>State</th>
                <th>Zip</th>
            </tr>
            </thead>
            </table>
            <button type="button" class="btn btn-primary" id="liveAlertBtn">Show live alert</button>
        </div>        
    </div>
    <div class="row mx-2">
        <p>
        You have visited this page {{ num_visits }} time{{ num_visits|pluralize }}.
        </p>
    </div>
<script>
    $(document).ready(function() {
        !function(n){n.fn.countdownTimer=function(o){let c=n.extend({seconds:5,loop:!0,callback:null},o);return this.each(function(){!function n(o,c,l,t,e){o.html(l);l--;-1===l&&t&&(l=c,null!==e&&e());l>-1?setTimeout(function(){n(o,c,l,t,e)},1e3):null!==e&&e()}(n(this),c.seconds,c.seconds,c.loop,c.callback)})}}(jQuery);        
        
        var alertPlaceholder = document.getElementById('liveAlertPlaceholder')
        var alertTrigger = document.getElementById('liveAlertBtn')
        
        function alert(message, type) {
          var wrapper = document.createElement('div')
          wrapper.innerHTML = '<div class="alert alert-' + type + ' alert-dismissible" role="alert">' + message + '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button></div>'
        
          alertPlaceholder.append(wrapper)
        }
        
        if (alertTrigger) {
          alertTrigger.addEventListener('click', function () {
            alert('Nice, you triggered this alert message!', 'success')
          })
        }
        
        new DataTable('#costquest', {
            columns: [
                { title: 'Location', data: 'location'},
                { title: 'Address', data: 'address'},
                { title: 'City', data: 'city'},
                { title: 'State', data: 'state'},
                { title: 'Zip', data: 'zip'},
            ],
            data: {{ data|safe }}
        });
    });
</script>
{% endblock %}