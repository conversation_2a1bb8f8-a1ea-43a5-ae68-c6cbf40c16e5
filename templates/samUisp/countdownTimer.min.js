/**
 * Author <PERSON> (c) 2021
 * Free to use
 * https://github.com/lee-ratinan/countdownTimer
 */
!function(n){n.fn.countdownTimer=function(o){let c=n.extend({seconds:5,loop:!0,callback:null},o);return this.each(function(){!function n(o,c,l,t,e){o.html(l);l--;-1===l&&t&&(l=c,null!==e&&e());l>-1?setTimeout(function(){n(o,c,l,t,e)},1e3):null!==e&&e()}(n(this),c.seconds,c.seconds,c.loop,c.callback)})}}(jQuery);