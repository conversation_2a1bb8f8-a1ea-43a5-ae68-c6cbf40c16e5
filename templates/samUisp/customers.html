{% extends "bootstrap/base.html" %}
{% block content %}
    <div class="row">
        <div class="col"><h1 class="display-6">SAM/UISP</h1></div>
    </div>
    <div class="row">
        <div class="col">
            <p>Active Customers:</p>
            <table id="activeCustomersTable" class="table table-striped table-hover compact" style="width: 100%">
            <thead>
            <tr>
                <th>Client ID</th>
                <th>Last Name</th>
                <th>First Name</th>
                <th>Address</th>
                <th>City</th>
                <th>Zip</th>
            </tr>
            </thead>
            </table>
            <p>Customer Leads:</p>
            <table id="customerLeadsTable" class="table table-striped table-hover compact" style="width: 100%">
            <thead>
            <tr>
                <th>Client ID</th>
                <th>Last Name</th>
                <th>First Name</th>
                <th>Address</th>
                <th>City</th>
                <th>Zip</th>
            </tr>
            </thead>
            </table>
        </div>
    </div>
    <div class="row">
        <div class="col"></div>
    </div>
<script>
    $(document).ready(function() {

        var table = $('#activeCustomersTable').DataTable({
           ajax: {
              processing: true,
              url: "{% url 'samUisp:json' %}",
                data: {
                  param: "activeCustomers",
               },
               dataSrc: "",
            },
            columnDefs: [
                {
                    targets: -1,
                    className: 'dt-body-left'
                }
            ],
            columns: [
                    { data: "clientId"},
                    { data: "lastName"},
                    { data: "firstName"},
                    { data: "street1"},
                    { data: 'city'},
                    { data: 'zipCode'},
                ],
        });

        var table = $('#customerLeadsTable').DataTable({
           ajax: {
              processing: true,
              url: "{% url 'samUisp:json' %}",
                data: {
                  param: "customerLeads",
               },
               dataSrc: "",
            },
            columnDefs: [
                {
                    targets: -1,
                    className: 'dt-body-left'
                }
            ],
            columns: [
                    { data: "clientId"},
                    { data: "lastName"},
                    { data: "firstName"},
                    { data: "street1"},
                    { data: 'city'},
                    { data: 'zipCode'},
                ],
        });
       
    });
</script>
{% endblock %}