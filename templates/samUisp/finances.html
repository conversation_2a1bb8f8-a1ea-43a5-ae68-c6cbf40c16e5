{% extends "bootstrap/base.html" %}
{% block content %}
    <div class="row">
        <div class="col"><h1 class="display-6">SAM/UISP</h1></div>
    </div>
    <div class="row">
        <div class="col">
            <p>Customers with open balance:</p>
            <table id="activetable" class="table table-striped table-hover compact" style="width: 100%">
            <thead>
            <tr>
                <th>Client ID</th>
                <th>Last Name</th>
                <th>First Name</th>
                <th>Address</th>
                <th>City</th>
                <th>Zip</th>
                <th>Recurring</th>
                <th>Outstanding</th>
            </tr>
            </thead>
            </table>
        </div>        
    </div>
    <div class="row">
        <div class="col">
            <script type="application/javascript">
                    google.load("visualization", "1", {packages:["corechart"]});
                </script>
                {{ chart_ccp.as_html }}
                {{ chart_off.as_html }}
        </div>
        <div class="col">
                {{ chart_wtn.as_html }}
                {{ chart_wts.as_html }}
        </div>
    </div>
<script>
    $(document).ready(function() {
        !function(n){n.fn.countdownTimer=function(o){let c=n.extend({seconds:5,loop:!0,callback:null},o);return this.each(function(){!function n(o,c,l,t,e){o.html(l);l--;-1===l&&t&&(l=c,null!==e&&e());l>-1?setTimeout(function(){n(o,c,l,t,e)},1e3):null!==e&&e()}(n(this),c.seconds,c.seconds,c.loop,c.callback)})}}(jQuery);        
        var table = $('#activetable').DataTable({
           ajax: {
              processing: true,
              url: "{% url 'samUisp:json' %}",
                data: {
                  param: "clients"
               },
               dataSrc: "",
            },
            columnDefs: [
                {
                    targets: -1,
                    className: 'dt-body-left'
                }
            ],
            columns: [
                    { data: "clientId"},
                    { data: "lastName"},
                    { data: "firstName"},
                    { data: "street1"},
                    { data: 'city'},
                    { data: 'zipCode'},
                    { data: 'recurring'},
                    { data: "outstanding"},
                ],
        });        
       
    });
</script>
{% endblock %}