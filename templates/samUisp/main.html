{% extends "bootstrap/base.html" %}
{% block content %}
    <div class="row mx-2">
        <div class="col"><h1 class="display-6">SAM/UISP</h1></div>
    </div>
    <div class="row mx-2">
        <p>Refresh in: <span class="Timer"></span> seconds:</p>
        <div class="col">
            <div class="row">
                <div class="col">
                    <p>ONU's Online for less than 1 Hour:</p>
                    <table id="onu_onehour" class="table table-striped table-hover compact" style="width: 100%">
                    <thead>
                    <tr>
                        <th>Name</th>
                        <th>Location</th>
                        <th>IP</th>
                        <th>Up</th>
                        <th>RX</th>
                        <th>Dist</th>
                    </tr>
                    </thead>
                    </table>
                </div>
            </div>
            <div class="row">
                <p>Offline ONU's:</p>
                <table id="offlinetable" class="table table-striped table-hover compact" style="width: 100%">
                <thead>
                <tr>
                    <th>Name</th>
                    <th>Status</th>
                    <th>Reason</th>
                    <th>Location</th>
                    <th>Offline for</th>
                </tr>
                </thead>
                </table>
                <!-- <button type="button" class="btn btn-primary" id="liveAlertBtn">Show live alert</button> -->
            </div>
        </div>
        <div class="col">            
            <div class="row">
                    <p>Inactive ONU's:</p>
                    <table id="inactivetable" class="table table-striped table-hover compact" style="width: 100%">
                    <thead>
                    <tr>
                        <th>Name</th>
                        <th>Status</th>
                        <th>Reason</th>
                        <th>Location</th>
                        <th>Offline for</th>
                    </tr>
                    </thead>
                    </table>
            </div>
            <div class="row">
                <p>Ping Times Across Camden Network: </p>
                <table id="oltStatus" class="table table-striped table-hover compact" style="width: 100%">
                <thead>
                <tr>
                    <th>Name</th>
                    <th>IP</th>
                    <th>Ping</th>
                </tr>
                </thead>
                </table>
            </div>
        </div>
    </div>
    <div class="row mx-2">
        <p>
        You have visited this page {{ num_visits }} time{{ num_visits|pluralize }}.
        </p>
    </div>
<script>
    $(document).ready(function() {
        !function(n){n.fn.countdownTimer=function(o){let c=n.extend({seconds:5,loop:!0,callback:null},o);return this.each(function(){!function n(o,c,l,t,e){o.html(l);l--;-1===l&&t&&(l=c,null!==e&&e());l>-1?setTimeout(function(){n(o,c,l,t,e)},1e3):null!==e&&e()}(n(this),c.seconds,c.seconds,c.loop,c.callback)})}}(jQuery);        
        
        var alertPlaceholder = document.getElementById('liveAlertPlaceholder')
        var alertTrigger = document.getElementById('liveAlertBtn')
        
        function alert(message, type) {
          var wrapper = document.createElement('div')
          wrapper.innerHTML = '<div class="alert alert-' + type + ' alert-dismissible" role="alert">' + message + '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button></div>'
        
          alertPlaceholder.append(wrapper)
        }
        
        if (alertTrigger) {
          alertTrigger.addEventListener('click', function () {
            alert('Nice, you triggered this alert message!', 'success')
          })
        }
        var table0 = $('#offlinetable').DataTable({
           ajax: {
              processing: true,
              url: "{% url 'samUisp:json' %}",
                data: {
                  param: "allOfflineDevices"
               },
               dataSrc: "",
            },
            order: [
                [4,'desc']
            ],
            columnDefs: [
                {
                    targets: -1,
                    className: 'dt-body-left'
                }
            ],
            columns: [
                    { data: "name"},
                    { data: "status"},
                    { data: "reason",
                    "render": function(data){
                        if (data === "Offline") {
                            return "<div style='color:red'>Offline</div>";
                        }   else {
                        return "<div style='color:forestgreen'>"+data+"</div>"
                        }
                    }},
                    { data: "location"},
                    { data: 'delta_time'},
                ],
        });
        var table1 = $('#inactivetable').DataTable({
           ajax: {
              processing: true,
              url: "{% url 'samUisp:json' %}",
                data: {
                  param: "allInactiveDevices"
               },
               dataSrc: "",
            },
            order: [
                [4,'desc']
            ],
            columnDefs: [
                {
                    targets: -1,
                    className: 'dt-body-left'
                }
            ],
            columns: [                    
                    { data: "name"},
                    { data: "status"},
                    { data: "reason",
                    "render": function(data){ 
                        if (data === "Offline") {
                            return "<div style='color:red'>Offline</div>";
                        }   else {
                        return "<div style='color:forestgreen'>"+data+"</div>"
                        }
                    }},
                    { data: "location"},
                    { data: 'delta_time'},                                        
                ],
        });
        var table2 = $('#oltStatus').DataTable({
           ajax: {
              processing: true,
              url: "{% url 'samUisp:json' %}",
                data: {
                  param: "ping_olts"
               },
               dataSrc: "",
            },
            paging: false,
            layout: {
               topEnd: null,
            },
            columnDefs: [
                {
                    targets: -1,
                    className: 'dt-body-left'
                }
            ],
            columns: [
                    { data: "name"},
                    { data: "ip"},
                    { data: "ping"},
                ],
        });
        var table3 = $('#onu_onehour').DataTable({
           ajax: {
              processing: true,
              url: "{% url 'samUisp:json' %}",
                data: {
                  param: "get_onus_one_hour"
               },
               dataSrc: "",
            },
            columnDefs: [
                {
                    targets: -1,
                    className: 'dt-body-left'
                }
            ],
            columns: [
                    { data: "hostname"},
                    { data: "location"},
                    { data: "ipAddress"},
                    { data: 'uptime',
                        render: function(data){
                            if( data == null) {
                                return 'Offline';
                            } else {
                                return new Date(data * 1000).toISOString().substring(14, 19)
                                var hours = Math.round(data / 3600).toFixed(0);
                                var minutes = Math.round((data % 3600)/60).toFixed(0);
                                var seconds = Math.round(data % 60).toFixed(0 );
                                return hours + ':' + minutes + ':' + seconds;
                            }
                        }},
                    { data: 'rx'},
                    { data: 'distance',
                        render: function(data){
                            var dist = Math.round(data / 100) / 10;
                            return dist + 'km';
                        }},
                ],
        });
        // https://www.jqueryscript.net/time-clock/countdown-timer-seconds.html
        $('.Timer').countdownTimer({
          seconds: 10,
          callback: function(){
            table0.ajax.reload()
            table1.ajax.reload()
            table2.ajax.reload()
            table3.ajax.reload()  
          }});
        
        
        
    });
</script>
{% endblock %}