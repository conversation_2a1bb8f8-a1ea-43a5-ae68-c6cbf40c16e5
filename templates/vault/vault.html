{%  extends "bootstrap/base.html" %}
{% block content %}
    <div class="pageholder">
        {% include "google/../bootstrap/navi.html" %}
        <div class="row mx-2">
            <div class="col"><h1 class="display-6">FiberPlan</h1></div>
        </div>
        <div class="row mx-2">
            <div class="col">
                <h3>Vault {{ vaults.0.name }}</h3>
                <table id="table" class="table table-striped table-hover" >
                    <thead>
                    <tr>
                        <th>Name</th>
                        <th>Lat</th>
                        <th>Lng</th>
                        <th>Address</th>
                        <th>MiniVault</th>
                        <th>Enclosure</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for vault in vaults %}
                        <tr>
                            <td>{{ vault.name }}</td>
                            <td>{{ vault.lat }}</td>
                            <td>{{ vault.lng }}</td>
                            <td>{{ vault.address }}</td>
                            <td>{{ vault.is_mini }}</td>
                            <td>{{ vault.is_enc }}</td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
<script>
    $(document).ready(function() {
        $('#test').DataTable();
    } );
</script>
{% endblock %}