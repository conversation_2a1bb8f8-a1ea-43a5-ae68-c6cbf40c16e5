{% extends 'bootstrap/base.html' %}
{% block content %}
        {% include "bootstrap/navi.html" %}
        <div class="row mx-2">
        <div class="col"><h1 class="display-6">FiberPlan</h1></div>
    </div>
    <div class="row mx-2">
        <div class="col">
            <table id="table" class="table table-striped table-hover" >
                <thead>
                <tr>                    
                    <th>Name</th>
                    <th>Address</th>
                    <th>Mini</th>
                    <th>Enclosure</th>
                    <th>Lat</th>
                    <th>Lng</th>                    
                </tr>
                </thead>
            </table>
        </div>

<script>
    $(document).ready(function() {
        var table = $('#table').DataTable({
           "ajax": {
               processing: true,
               url: "{% url 'fiberPlan:jsonresponse' %}",
               data: {
                   param: 'get_all_vaults'
               },
               dataSrc: "",
            }, 
            columns: [                    
                    { data: "name"},
                    { data: "address"},
                    { data: "is_mini",
                     render: function(data){ if (data == false) return "No"; return "Yes"}
                    },
                    { data: "is_enc",
                     render: function (data) { if (data === false) return "No"; return "Yes"}
                    },
                    { data: "lat"},
                    { data: "lng"},                    
                ],
        });
    });
</script>
{% endblock %}