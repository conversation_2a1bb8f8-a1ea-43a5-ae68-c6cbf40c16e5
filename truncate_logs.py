#!/usr/bin/env python3
import os
import logging

def truncate_logs(log_dir):
    """Truncate all log files in the specified directory to 0 bytes."""
    print(f"Truncating logs in directory: {log_dir}")
    
    # Check if directory exists
    if not os.path.isdir(log_dir):
        print(f"Error: Directory '{log_dir}' does not exist.")
        return False
    
    truncated_count = 0
    error_count = 0
    
    # List all files in the directory
    for filename in os.listdir(log_dir):
        file_path = os.path.join(log_dir, filename)
        
        # Check if it's a file and has a .log extension
        if os.path.isfile(file_path) and filename.endswith('.log'):
            try:
                # Open the file in write mode with truncate
                with open(file_path, 'w') as f:
                    pass  # Just opening in 'w' mode truncates the file
                
                print(f"Truncated: {file_path}")
                truncated_count += 1
            except Exception as e:
                print(f"Error truncating {file_path}: {str(e)}")
                error_count += 1
    
    print(f"\nSummary:")
    print(f"- {truncated_count} log files truncated")
    print(f"- {error_count} errors encountered")
    
    return truncated_count > 0

if __name__ == "__main__":
    # Use the log directory from your Django project
    # Based on your setup scripts, this is likely /var/log/hotspot
    # For local development, adjust this path as needed
    log_dir = "C:\\Users\\<USER>\\source\\repos\\hotspot\\logs" # Change this to your local logs directory
    
    # If you want to specify the directory as a command-line argument
    import sys
    if len(sys.argv) > 1:
        log_dir = sys.argv[1]
    
    truncate_logs(log_dir)